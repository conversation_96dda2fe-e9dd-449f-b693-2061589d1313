package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import timber.log.Timber
import java.util.ArrayDeque
import javax.inject.Inject
import javax.inject.Singleton

/**
 * SegmentQueueReducer - 基于Segment队列模型的Reducer（729方案3.md）
 *
 * 🎯 核心职责：
 * - 实现Segment队列管理：创建→填充→闭合→渲染→移除
 * - 处理三断点规则：<thinking>、</phase>、</thinking>
 * - 管理双时序架构：Token数据流 + UI渲染队列
 * - 支持finalBuffer后台累积
 *
 * 🔥 架构原则：
 * - 纯函数Reducer，无副作用
 * - 队列保证UI渲染顺序
 * - 状态不可变，支持时间旅行调试
 */
@Singleton
class SegmentQueueReducer @Inject constructor() {

    /**
     * ReduceResult - Reducer结果包装（支持History写入方案B）
     */
    data class ReduceResult(
        val state: TBState,
        val effects: List<ThinkingBoxContract.Effect> = emptyList(),
    )

    /**
     * TBState - Segment队列架构状态（729方案3.md）
     * 替换原有复杂状态结构，采用简化的队列模型
     */
    data class TBState(
        val current: Segment? = null, // 当前写入中的段
        val queue: ArrayDeque<Segment> = ArrayDeque(), // 闭合待渲染段队列
        val finalBuffer: StringBuilder = StringBuilder(), // <final>后的所有token
        val thinkingClosed: Boolean = false, // 是否收到</thinking>
        val finalClosed: Boolean = false, // 是否收到</final>
        // 🔥 【729方案9优化】streaming标志改为Flow-derived，不再手动维护

        // 元数据
        val messageId: String? = null,
        val startTime: Long = System.currentTimeMillis(),
        val version: Long = 0L, // 用于触发UI重组
    ) {
        /**
         * 🔥 【729方案9优化】获取下一个待渲染的段
         * current + queue并行输出：当前段如果closed==true && text.length>0立即加入渲染队列
         */
        fun getNextSegmentToRender(): Segment? {
            // 优先检查current段是否可以渲染
            current?.let { currentSegment ->
                if (currentSegment.closed && currentSegment.content.isNotEmpty()) {
                    return currentSegment
                }
            }
            // 然后检查队列头部
            return queue.firstOrNull()
        }

        /**
         * 判断思考框是否应该关闭
         * 条件：思考阶段结束 && 队列为空
         */
        fun shouldCloseThinkingBox(): Boolean = thinkingClosed && queue.isEmpty()

        /**
         * 🔥 【729方案9优化】判断最终内容是否准备好渲染
         * 放宽条件：思考阶段结束 && finalBuffer不为空 && UI空闲
         * 支持并行渲染路径，不需要等待队列完全清空
         */
        fun isFinalReadyToRender(): Boolean = thinkingClosed && finalBuffer.isNotEmpty() && isUiIdle()

        /**
         * 🔥 【729方案9优化】判断UI是否空闲（可以开始渲染final内容）
         * 条件：队列为空或只有一个正在渲染的段
         */
        private fun isUiIdle(): Boolean = queue.isEmpty() || queue.size <= 1

        /**
         * 🔥 【729方案9优化】计算streaming状态（Flow-derived）
         * 条件：思考阶段未结束 或 final内容未结束
         */
        fun isStreaming(): Boolean = !thinkingClosed || !finalClosed

        /**
         * 获取最终内容文本
         */
        fun getFinalContent(): String = finalBuffer.toString()

        /**
         * 计算总段数（用于调试和监控）
         */
        fun getTotalSegmentCount(): Int = queue.size + (if (current != null) 1 else 0)

        /**
         * 获取状态摘要（用于日志）
         */
        fun getSummary(): String = "TBState(current=${current?.id}, queue=${queue.size}, " +
            "finalBuffer=${finalBuffer.length}, thinkingClosed=$thinkingClosed, " +
            "finalClosed=$finalClosed, streaming=${isStreaming()})"
    }

    /**
     * 主Reduce方法 - 处理ThinkingEvent并更新状态（支持History写入方案B）
     *
     * @param state 当前状态
     * @param event 输入事件
     * @return ReduceResult包含新状态和History写入Effects
     */
    fun reduce(state: TBState, event: ThinkingEvent): ReduceResult {
        Timber.tag("TB-REDUCER").d("🔄 处理事件: ${event::class.simpleName}")

        val result = when (event) {
            // ===== Segment生命周期事件 =====

            is ThinkingEvent.SegmentStarted -> {
                Timber.tag("TB-REDUCER").d("🎯 创建段: ${event.id} (${event.kind.name})")

                // 如果有当前段，先闭合它
                val updatedQueue = if (state.current != null) {
                    val closedCurrent = state.current.copy(closed = true)
                    ArrayDeque(state.queue).apply { addLast(closedCurrent) }
                } else {
                    state.queue
                }

                // 创建新段
                val newSegment = Segment(
                    id = event.id,
                    kind = event.kind,
                    title = event.title,
                    content = "", // 🔥 【MVI修复】使用content字段替代text+StringBuilder
                    closed = false,
                    rendered = false,
                )

                ReduceResult(
                    state = state.copy(
                        current = newSegment,
                        queue = updatedQueue,
                        version = state.version + 1,
                    ),
                )
            }

            is ThinkingEvent.SegmentText -> {
                if (state.current != null) {
                    Timber.tag("TB-REDUCER").d("📝 追加文本到段[${state.current.id}]: ${event.text.take(30)}...")
                    // 🔥 【MVI修复】使用不可变Segment API替代可变StringBuilder
                    val updatedCurrent = state.current.appendContent(event.text)

                    ReduceResult(
                        state = state.copy(
                            current = updatedCurrent,
                            version = state.version + 1
                        ),
                    )
                } else {
                    Timber.tag("TB-REDUCER").w("❌ 无当前段，忽略文本: ${event.text.take(30)}...")
                    ReduceResult(state = state)
                }
            }

            is ThinkingEvent.SegmentClosed -> {
                Timber.tag("TB-REDUCER").d("🔒 闭合段: ${event.id}")

                if (state.current?.id == event.id) {
                    // 闭合当前段并加入队列
                    val closedSegment = state.current.copy(closed = true)
                    val updatedQueue = ArrayDeque(state.queue).apply { addLast(closedSegment) }

                    ReduceResult(
                        state = state.copy(
                            current = null,
                            queue = updatedQueue,
                            version = state.version + 1,
                        ),
                    )
                } else {
                    // 段ID不匹配，记录警告但不更新状态
                    Timber.tag("TB-REDUCER").w("❌ 段ID不匹配: 期望=${state.current?.id}, 实际=${event.id}")
                    ReduceResult(state = state)
                }
            }

            is ThinkingEvent.UiSegmentRendered -> {
                Timber.tag("TB-REDUCER").d("✅ [UiSegmentRendered] UI完成渲染段: ${event.id}")

                // 🔥 【关键修复】优先从队列中移除已渲染的段
                val updatedQueue = ArrayDeque(state.queue)
                var segmentRemoved = false

                if (updatedQueue.firstOrNull()?.id == event.id) {
                    updatedQueue.removeFirst()
                    segmentRemoved = true
                    Timber.tag("TB-QUEUE").d("📤 队列移除段: ${event.id}, 剩余: ${updatedQueue.size}")
                }

                // 🔥 【finalmermaid规范修复】perthink段不能在UI渲染完成后被清理
                val updatedCurrent = if (!segmentRemoved && state.current?.id == event.id) {
                    // 检查是否为perthink段
                    if (state.current?.kind == SegmentKind.PERTHINK) {
                        Timber.tag("TB-REDUCER").d("🔄 [finalmermaid规范] perthink段渲染完成但保持活跃: ${event.id}")
                        // perthink段保持活跃状态，继续接收内容，直到<thinking>标签
                        state.current
                    } else {
                        // 非perthink段（如PHASE段）可以在渲染完成后清理
                        Timber.tag("TB-REDUCER").d("🧹 清理已完成的${state.current?.kind}段: ${event.id}")
                        null
                    }
                } else {
                    state.current
                }

                // 如果两个地方都没找到，记录警告
                if (!segmentRemoved && state.current?.id != event.id) {
                    Timber.tag("TB-REDUCER").w("⚠️ [UiSegmentRendered] 段未找到: ${event.id}, 队列头=${updatedQueue.firstOrNull()?.id}, current=${state.current?.id}")
                }

                ReduceResult(
                    state = state.copy(
                        queue = updatedQueue,
                        current = updatedCurrent,
                        version = state.version + 1,
                    ),
                )
            }

            // ===== 思考阶段控制事件 =====

            is ThinkingEvent.ThinkingClosed -> {
                Timber.tag("TB-REDUCER").d("🔚 [finalmermaid规范] 思考阶段结束，触发History写入")

                // 🔥 【finalmermaid规范】不在Reducer中创建final-phase段，由DomainMapper负责
                // final-phase段已经由DomainMapper在</thinking>断点时创建并入队

                // 🔥 【History写入方案B】思考阶段结束时写入思考历史
                val thinkingMarkdown = buildThinkingMarkdown(state)
                val historyEffect = if (thinkingMarkdown.isNotEmpty() && state.messageId != null) {
                    listOf(
                        ThinkingBoxContract.Effect.NotifyHistoryThinking(
                            messageId = state.messageId,
                            thinkingMarkdown = thinkingMarkdown,
                        ),
                    )
                } else {
                    emptyList()
                }

                // 🔥 【finalmermaid规范】只有当队列为空时才触发CloseThinkingBox Effect
                val closeEffect = if (state.queue.isEmpty()) {
                    listOf(ThinkingBoxContract.Effect.CloseThinkingBox)
                } else {
                    emptyList()
                }

                ReduceResult(
                    state = state.copy(
                        thinkingClosed = true,
                        version = state.version + 1,
                    ),
                    effects = historyEffect + closeEffect,
                )
            }

            // ===== Final内容处理事件 =====

            is ThinkingEvent.FinalStart -> {
                Timber.tag("TB-REDUCER").d("🔥 [FinalStart] 激活finalBuffer")
                ReduceResult(
                    state = state.copy(version = state.version + 1),
                )
            }

            is ThinkingEvent.FinalContent -> {
                Timber.tag("TB-REDUCER").d("📄 [FinalContent] 追加final内容: ${event.text.take(50)}...")
                state.finalBuffer.append(event.text)
                ReduceResult(
                    state = state.copy(version = state.version + 1),
                )
            }

            is ThinkingEvent.FinalComplete -> {
                Timber.tag("TB-REDUCER").d("🏁 [FinalComplete] Final内容完成，触发History写入")

                // 🔥 【History写入方案B】Final内容完成时写入最终答案历史
                val finalMarkdown = state.getFinalContent()
                val historyEffect = if (finalMarkdown.isNotEmpty() && state.messageId != null) {
                    listOf(
                        ThinkingBoxContract.Effect.NotifyHistoryFinal(
                            messageId = state.messageId,
                            finalMarkdown = finalMarkdown,
                        ),
                    )
                } else {
                    emptyList()
                }

                ReduceResult(
                    state = state.copy(
                        finalClosed = true,
                        version = state.version + 1,
                    ),
                    effects = historyEffect,
                )
            }

            // ===== 错误处理事件 =====

            is ThinkingEvent.ParseError -> {
                Timber.tag("TB-REDUCER").e("❌ [ParseError] 解析错误: ${event.message}")
                // 解析错误不改变状态，只记录日志
                ReduceResult(state = state)
            }
        }

        // 记录状态变化
        if (result.state.version != state.version) {
            Timber.tag("TB-REDUCER").d("📊 状态更新: ${result.state.getSummary()}")
        }

        // 记录Effects
        if (result.effects.isNotEmpty()) {
            Timber.tag("TB-REDUCER").d("🔥 [History写入] 生成Effects: ${result.effects.size}个")
            result.effects.forEach { effect ->
                Timber.tag("TB-REDUCER").d("  - ${effect::class.simpleName}")
            }
        }

        return result
    }

    /**
     * 批量处理事件 - 优化性能（支持History写入方案B）
     */
    fun reduceBatch(state: TBState, events: List<ThinkingEvent>): ReduceResult {
        val allEffects = mutableListOf<ThinkingBoxContract.Effect>()
        val finalState = events.fold(state) { currentState, event ->
            val result = reduce(currentState, event)
            allEffects.addAll(result.effects)
            result.state
        }
        return ReduceResult(state = finalState, effects = allEffects)
    }

    /**
     * 🔥 【729方案9优化】构建思考历史的Markdown内容
     * 额外包含current.closed==true的段，确保历史记录完整
     */
    private fun buildThinkingMarkdown(state: TBState): String {
        val markdown = StringBuilder()

        // 🔥 【729方案9优化】添加当前段（如果已闭合且非空）
        state.current?.let { current ->
            if (current.closed && current.content.isNotEmpty()) {
                Timber.tag("TB-REDUCER").d("✅ [History] 包含已闭合的current段: ${current.id}")
                markdown.append("## ${current.title ?: "思考阶段"}\n")
                markdown.append(current.getTextContent())
                markdown.append("\n\n")
            } else if (current.content.isNotEmpty()) {
                // 记录未闭合但非空的段（调试用）
                Timber.tag("TB-REDUCER").d("⚠️ [History] 跳过未闭合的current段: ${current.id}, 长度=${current.content.length}")
            }
        }

        // 添加队列中的段
        state.queue.forEach { segment ->
            if (segment.content.isNotEmpty()) {
                markdown.append("## ${segment.title ?: "思考阶段"}\n")
                markdown.append(segment.getTextContent())
                markdown.append("\n\n")
            }
        }

        return markdown.toString().trim()
    }
}
