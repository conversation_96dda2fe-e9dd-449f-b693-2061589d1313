10:24:29.832 HTTP-RAW-RESPONSE        I  📊 [原始HTTP响应汇总] messageId=759b629b-594a-437b-8112-d693c5f7c57b, 累计=200个响应, XML标签=0个
10:24:29.832 HTTP-RAW-XML             W  ⚠️ [原始响应] 未检测到任何XML标签字符！
10:24:32.427 .example.gymbro          I  Background concurrent mark compact GC freed 22MB AllocSpace bytes, 19(460KB) LOS objects, 20% free, 93MB/117MB, paused 1.633ms,11.582ms total 637.959ms
10:24:39.709 JSON-STREAM-OUTPUT       I  🚀 [流式输出] messageId=759b629b-594a-437b-8112-d693c5f7c57b, 内容长度=18
10:24:39.722 CNT-XML-DEBUG            E  🔍 [XML Token检测] messageId=759b629b-594a-437b-8112-d693c5f7c57b, token='器械或弹力带更安全。</phase>'
10:24:39.723 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=759b629b-594a-437b-8112-d693c5f7c57b, token长度=18, 内容预览='器械或弹力带更安全。</phase>'
10:24:39.723 CNT-TOKEN-ROUTER         D  🔥 路由Token到messageId=759b629b-594a-437b-8112-d693c5f7c57b, token长度=18
10:24:39.724 ConversationScope        D  🔍 [XML Token传递] messageId=759b629b-594a-437b-8112-d693c5f7c57b, token='器械或弹力带更安全。</phase>'
10:24:39.724 TB-PARSER                D  📥 [收到Token] #1: '器械或弹力带更安全。</phase>'
10:24:39.724 TB-RAW-COL...AGGREGATED  I  🔍 [聚合] 1条消息/2个token: [1][StreamingThinkingMLParser] 器械或弹力带更安全。</phase>
10:24:39.724 TB-PARSER                D  🏷️ [关键标签] 检测到重要标签在 token 中
10:24:39.725 TB-XML-INPUT             E  🔍 [XML扫描器输入] 长度=18: '器械或弹力带更安全。</phase>'
10:24:39.725 TB-XML-BUFFER            E  🔍 [缓冲区状态] 长度=18, 内容='器械或弹力带更安全。</phase>'
10:24:39.726 TB-XML-SCANNER           V  🔍 [标签解析] 尝试解析: '/phase'
10:24:39.727                          V  🔍 [标签解析] 清理后内容: '/phase'
10:24:39.727                          V  🔍 [标签解析] ✅ 识别为关闭标签: 'phase'
10:24:39.735 TB-XML-OUTPUT            E  🔍 [Token输出] 生成2个tokens:
10:24:39.742                          E  🔍 [Token输出] [0] Text(content=器械或弹力带更安全。)
10:24:39.743                          E  🔍 [Token输出] [1] TagClose(name=phase)
10:24:39.744 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入18字符, 输出2个Token, 缓冲剩余0字符
10:24:39.744 TB-PARSER                V  📝 [文本内容] 器械或弹力带更安全。... in state: PRE_THINK
10:24:39.745 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
10:24:39.746 TB-MAPPER                I  📝 [finalmermaid规范] PRE_THINK状态，自动创建perthink段
10:24:39.752 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentStarted
10:24:39.752 TB-REDUCER               D  🔄 处理事件: SegmentStarted
10:24:39.752                          D  🎯 创建段: perthink (PERTHINK)
10:24:39.758                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:39.759 TB-CONVERT               D  ✅ 添加当前段到队列: perthink (PERTHINK)
10:24:39.759                          D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
10:24:39.761 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentText
10:24:39.761 TB-REDUCER               D  🔄 处理事件: SegmentText
10:24:39.761                          D  📝 追加文本到段[perthink]: 器械或弹力带更安全。...
10:24:39.762                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:39.762 TB-CONVERT               D  ✅ 添加当前段到队列: perthink (PERTHINK)
10:24:39.762                          D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
10:24:39.768 TB-PARSER                V  🏷️ [标签关闭] </phase> in state: PRE_THINK
10:24:39.771 TB-VIEWMODEL             D  🔍 [语义事件] TagClosed
10:24:39.771 TB-MAPPER                W  ⚠️ [finalmermaid规范] </phase>标签在PRE_THINK状态下被忽略，perthink段只能被<thinking>关闭
10:24:39.771 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=759b629b-594a-437b-8112-d693c5f7c57b, 总计1个tokens
10:24:39.771 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由1个tokens
10:24:39.771 CNT-TOKEN-ROUTER         V  🔥 Token路由成功，总计路由1个tokens
10:24:39.783 TB-DISPLAY               D  ✅ 显示条件满足: 有1个段需要显示
10:24:39.783 TB-RENDER                D  🎯 显示思考内容: perthink (PERTHINK), 内容=10字符
10:24:39.865 TB-UI                    D  📊 Segment状态: 队列头段=perthink, 队列大小=1, 思考关闭=false, 流式=true
10:24:40.021                          D  ✅ 段渲染完成: perthink
10:24:40.022 TB-CALLBACK              D  📤 段渲染完成回调: perthink
10:24:40.024 TB-REDUCER               D  🔄 处理事件: UiSegmentRendered
10:24:40.024                          D  ✅ [UiSegmentRendered] UI完成渲染段: perthink
10:24:40.025                          D  🔄 [finalmermaid规范] perthink段渲染完成但保持活跃: perthink
10:24:40.025                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:40.025 TB-CONVERT               D  ✅ 添加当前段到队列: perthink (PERTHINK)
10:24:40.025                          D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
10:24:44.920 JSON-STREAM-OUTPUT       I  🚀 [流式输出] messageId=759b629b-594a-437b-8112-d693c5f7c57b, 内容长度=10
10:24:44.921 CNT-XML-DEBUG            E  🔍 [XML Token检测] messageId=759b629b-594a-437b-8112-d693c5f7c57b, token='竭。</phase>'
10:24:44.921 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=759b629b-594a-437b-8112-d693c5f7c57b, token长度=10, 内容预览='竭。</phase>'
10:24:44.921 CNT-TOKEN-ROUTER         D  🔥 路由Token到messageId=759b629b-594a-437b-8112-d693c5f7c57b, token长度=10
10:24:44.921 ConversationScope        D  🔍 [XML Token传递] messageId=759b629b-594a-437b-8112-d693c5f7c57b, token='竭。</phase>'
10:24:44.921 TB-PARSER                D  📥 [收到Token] #2: '竭。</phase>'
10:24:44.922 TB-RAW-COL...AGGREGATED  I  🔍 [聚合] 5条消息/15个token: [EVENT][TB-XML-SCANNER] XMLToken: Text(content=器械或弹力带更安全。) [EVENT][processXmlToken] SemanticEvent: TextChunk(text=器械或弹力带更安全。) [EVENT][TB-XML-SCANNER] XMLToken: TagClose(name=phase) [EVENT][processXmlT...
10:24:44.922 TB-PARSER                D  🏷️ [关键标签] 检测到重要标签在 token 中
10:24:44.922 TB-XML-INPUT             E  🔍 [XML扫描器输入] 长度=10: '竭。</phase>'
10:24:44.922 TB-XML-BUFFER            E  🔍 [缓冲区状态] 长度=10, 内容='竭。</phase>'
10:24:44.922 TB-XML-SCANNER           V  🔍 [标签解析] 尝试解析: '/phase'
10:24:44.922                          V  🔍 [标签解析] 清理后内容: '/phase'
10:24:44.922                          V  🔍 [标签解析] ✅ 识别为关闭标签: 'phase'
10:24:44.922 TB-XML-OUTPUT            E  🔍 [Token输出] 生成2个tokens:
10:24:44.923                          E  🔍 [Token输出] [0] Text(content=竭。)
10:24:44.923                          E  🔍 [Token输出] [1] TagClose(name=phase)
10:24:44.923 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入10字符, 输出2个Token, 缓冲剩余0字符
10:24:44.923 TB-PARSER                V  📝 [文本内容] 竭。... in state: PRE_THINK
10:24:44.923 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
10:24:44.923                          D  🔄 [处理ThinkingEvent] SegmentText
10:24:44.923 TB-REDUCER               D  🔄 处理事件: SegmentText
10:24:44.927                          D  📝 追加文本到段[perthink]: 竭。...
10:24:44.927                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:44.927 TB-CONVERT               D  ✅ 添加当前段到队列: perthink (PERTHINK)
10:24:44.927                          D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
10:24:44.927 TB-PARSER                V  🏷️ [标签关闭] </phase> in state: PRE_THINK
10:24:44.927 TB-VIEWMODEL             D  🔍 [语义事件] TagClosed
10:24:44.928 TB-MAPPER                W  ⚠️ [finalmermaid规范] </phase>标签在PRE_THINK状态下被忽略，perthink段只能被<thinking>关闭
10:24:44.928 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=759b629b-594a-437b-8112-d693c5f7c57b, 总计2个tokens
10:24:44.928 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由2个tokens
10:24:44.928 CNT-TOKEN-ROUTER         V  🔥 Token路由成功，总计路由2个tokens
10:24:44.934 TB-DISPLAY               D  ✅ 显示条件满足: 有1个段需要显示
10:24:44.934 TB-RENDER                D  🎯 显示思考内容: perthink (PERTHINK), 内容=12字符
10:24:45.142 TB-UI                    D  ✅ 段渲染完成: perthink
10:24:45.145 TB-CALLBACK              D  📤 段渲染完成回调: perthink
10:24:45.145 TB-REDUCER               D  🔄 处理事件: UiSegmentRendered
10:24:45.146                          D  ✅ [UiSegmentRendered] UI完成渲染段: perthink
10:24:45.146                          D  🔄 [finalmermaid规范] perthink段渲染完成但保持活跃: perthink
10:24:45.146                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:45.146 TB-CONVERT               D  ✅ 添加当前段到队列: perthink (PERTHINK)
10:24:45.146                          D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
10:24:49.026 HTTP-RAW-RESPONSE        I  📊 [原始HTTP响应汇总] messageId=759b629b-594a-437b-8112-d693c5f7c57b, 累计=400个响应, XML标签=0个
10:24:49.027 HTTP-RAW-XML             W  ⚠️ [原始响应] 未检测到任何XML标签字符！
10:24:50.485 JSON-STREAM-OUTPUT       I  🚀 [流式输出] messageId=759b629b-594a-437b-8112-d693c5f7c57b, 内容长度=13
10:24:50.504 CNT-XML-DEBUG            E  🔍 [XML Token检测] messageId=759b629b-594a-437b-8112-d693c5f7c57b, token='>
                                         </thinking>'
10:24:50.504 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=759b629b-594a-437b-8112-d693c5f7c57b, token长度=13, 内容预览='>
                                         </thinking>'
10:24:50.504 CNT-TOKEN-ROUTER         D  🔥 路由Token到messageId=759b629b-594a-437b-8112-d693c5f7c57b, token长度=13
10:24:50.505 ConversationScope        D  🔍 [XML Token传递] messageId=759b629b-594a-437b-8112-d693c5f7c57b, token='>
                                         </thinking>'
10:24:50.505 TB-PARSER                D  📥 [收到Token] #3: '>
                                         </thinking>'
10:24:50.505 TB-RAW-COL...AGGREGATED  I  🔍 [聚合] 5条消息/16个token: [EVENT][TB-XML-SCANNER] XMLToken: Text(content=竭。) [EVENT][processXmlToken] SemanticEvent: TextChunk(text=竭。) [EVENT][TB-XML-SCANNER] XMLToken: TagClose(name=phase) [EVENT][processXmlToken] SemanticEv...
10:24:50.507 TB-PARSER                D  🏷️ [关键标签] 检测到重要标签在 token 中
10:24:50.508 TB-XML-INPUT             E  🔍 [XML扫描器输入] 长度=13: '>
                                         </thinking>'
10:24:50.508 TB-XML-BUFFER            E  🔍 [缓冲区状态] 长度=13, 内容='>
                                         </thinking>'
10:24:50.508 TB-XML-SCANNER           V  🔍 [标签解析] 尝试解析: '/thinking'
10:24:50.508                          V  🔍 [标签解析] 清理后内容: '/thinking'
10:24:50.508                          V  🔍 [标签解析] ✅ 识别为关闭标签: 'thinking'
10:24:50.508 TB-XML-OUTPUT            E  🔍 [Token输出] 生成2个tokens:
10:24:50.508                          E  🔍 [Token输出] [0] Text(content=>
                                         )
10:24:50.508                          E  🔍 [Token输出] [1] TagClose(name=thinking)
10:24:50.508 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入13字符, 输出2个Token, 缓冲剩余0字符
10:24:50.508 TB-PARSER                V  📝 [文本内容] >
                                         ... in state: PRE_THINK
10:24:50.508 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
10:24:50.508                          D  🔄 [处理ThinkingEvent] SegmentText
10:24:50.508 TB-REDUCER               D  🔄 处理事件: SegmentText
10:24:50.509                          D  📝 追加文本到段[perthink]: >
                                         ...
10:24:50.509                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:50.509 TB-CONVERT               D  ✅ 添加当前段到队列: perthink (PERTHINK)
10:24:50.509                          D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
10:24:50.509 TB-PARSER                V  🏷️ [标签关闭] </thinking> in state: PRE_THINK
10:24:50.509 TB-VIEWMODEL             D  🔍 [语义事件] TagClosed
10:24:50.510                          D  🔄 [处理ThinkingEvent] SegmentClosed
10:24:50.510 TB-REDUCER               D  🔄 处理事件: SegmentClosed
10:24:50.511                          D  🔒 闭合段: perthink
10:24:50.513                          D  📊 状态更新: TBState(current=null, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:50.514 TB-CONVERT               D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
10:24:50.514 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentStarted
10:24:50.515 TB-REDUCER               D  🔄 处理事件: SegmentStarted
10:24:50.515                          D  🎯 创建段: final-phase (PHASE)
10:24:50.515                          D  📊 状态更新: TBState(current=final-phase, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:50.515 TB-CONVERT               D  ✅ 添加当前段到队列: final-phase (PHASE)
10:24:50.516                          D  📊 Contract状态: 队列=2段, 流式=true, 思考关闭=false
10:24:50.516 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentClosed
10:24:50.516 TB-REDUCER               D  🔄 处理事件: SegmentClosed
10:24:50.516                          D  🔒 闭合段: final-phase
10:24:50.516                          D  📊 状态更新: TBState(current=null, queue=2, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
10:24:50.516 TB-CONVERT               D  📊 Contract状态: 队列=2段, 流式=true, 思考关闭=false
10:24:50.517 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] ThinkingClosed
10:24:50.517 TB-REDUCER               D  🔄 处理事件: ThinkingClosed
10:24:50.517                          D  🔚 [finalmermaid规范] 思考阶段结束，触发History写入
10:24:50.522                          D  📊 状态更新: TBState(current=null, queue=2, finalBuffer=0, thinkingClosed=true, finalClosed=false, streaming=true)
10:24:50.531                          D  🔥 [History写入] 生成Effects: 1个
10:24:50.532                          D    - NotifyHistoryThinking
10:24:50.532 TB-CONVERT               D  📊 Contract状态: 队列=2段, 流式=true, 思考关闭=true
10:24:50.533 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=759b629b-594a-437b-8112-d693c5f7c57b, 总计3个tokens
10:24:50.533 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由3个tokens
10:24:50.533 CNT-TOKEN-ROUTER         V  🔥 Token路由成功，总计路由3个tokens
10:24:50.537 TB-DISPLAY               D  ✅ 显示条件满足: 有2个段需要显示
10:24:50.538 TB-RENDER                D  🎯 显示思考内容: perthink (PERTHINK), 内容=14字符
10:24:50.567 TB-UI                    D  📊 Segment状态: 队列头段=perthink, 队列大小=2, 思考关闭=true, 流式=true
10:24:50.713                          D  ✅ 段渲染完成: perthink
10:24:50.713 TB-CALLBACK              D  📤 段渲染完成回调: perthink
10:24:50.714 TB-REDUCER               D  🔄 处理事件: UiSegmentRendered
10:24:50.714                          D  ✅ [UiSegmentRendered] UI完成渲染段: perthink
10:24:50.714                          D  📊 状态更新: TBState(current=null, queue=1, finalBuffer=0, thinkingClosed=true, finalClosed=false, streaming=true)
10:24:50.714 TB-CONVERT               D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=true
10:24:50.717 TB-DISPLAY               D  ✅ 显示条件满足: 有1个段需要显示
10:24:50.718 TB-RENDER                D  ❌ 无显示内容: 流式=true, 思考关闭=true
10:24:50.731 TB-UI                    D  📊 Segment状态: 队列头段=final-phase, 队列大小=1, 思考关闭=true, 流式=true
