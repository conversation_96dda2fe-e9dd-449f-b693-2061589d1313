package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.util.ArrayDeque
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SegmentQueueReducer单元测试
 *
 * 🎯 测试目标：验证Segment队列架构的状态管理
 * 📊 覆盖率目标：≥90%
 * 🔧 测试框架：JUnit 5 + kotlin.test
 * 🔥 重点：P0修复后的队列管理和History写入验证
 */
@DisplayName("SegmentQueueReducer")
class SegmentQueueReducerTest {

    private lateinit var reducer: SegmentQueueReducer
    private lateinit var initialState: SegmentQueueReducer.TBState

    @BeforeEach
    fun setUp() {
        reducer = SegmentQueueReducer()
        initialState = SegmentQueueReducer.TBState(messageId = "test-message")
    }

    @Nested
    @DisplayName("Segment生命周期事件测试")
    inner class SegmentLifecycleTests {

        @Test
        @DisplayName("SegmentStarted应该创建新段")
        fun `SegmentStarted should create new segment`() {
            // Given
            val event = ThinkingEvent.SegmentStarted("perthink", SegmentKind.PERTHINK, "Bro is thinking")

            // When
            val result = reducer.reduce(initialState, event)

            // Then
            assertNotNull(result.state.current)
            assertEquals("perthink", result.state.current!!.id)
            assertEquals(SegmentKind.PERTHINK, result.state.current!!.kind)
            assertEquals("Bro is thinking", result.state.current!!.title)
            assertFalse(result.state.current!!.closed)
            assertTrue(result.state.queue.isEmpty())
        }

        @Test
        @DisplayName("SegmentStarted应该闭合之前的当前段")
        fun `SegmentStarted should close previous current segment`() {
            // Given
            val stateWithCurrent = initialState.copy(
                current = Segment(
                    id = "old-segment",
                    kind = SegmentKind.PERTHINK,
                    title = "Old",
                    content = "old content",
                    closed = false,
                    rendered = false
                )
            )
            val event = ThinkingEvent.SegmentStarted("new-segment", SegmentKind.PHASE, "New")

            // When
            val result = reducer.reduce(stateWithCurrent, event)

            // Then
            // 验证新的当前段
            assertNotNull(result.state.current)
            assertEquals("new-segment", result.state.current!!.id)

            // 验证旧段被加入队列
            assertEquals(1, result.state.queue.size)
            val queuedSegment = result.state.queue.first()
            assertEquals("old-segment", queuedSegment.id)
            assertTrue(queuedSegment.closed)
        }

        @Test
        @DisplayName("SegmentText应该添加文本到当前段")
        fun `SegmentText should add text to current segment`() {
            // Given
            val stateWithCurrent = initialState.copy(
                current = Segment(
                    id = "test-segment",
                    kind = SegmentKind.PERTHINK,
                    title = "Test",
                    content = "initial ",
                    closed = false,
                    rendered = false
                )
            )
            val event = ThinkingEvent.SegmentText("additional text")

            // When
            val result = reducer.reduce(stateWithCurrent, event)

            // Then
            assertEquals("initial additional text", result.state.current!!.getTextContent())
        }

        @Test
        @DisplayName("SegmentClosed应该闭合指定段")
        fun `SegmentClosed should close specified segment`() {
            // Given
            val stateWithCurrent = initialState.copy(
                current = Segment(
                    id = "test-segment",
                    kind = SegmentKind.PERTHINK,
                    title = "Test",
                    content = "content",
                    closed = false,
                    rendered = false
                )
            )
            val event = ThinkingEvent.SegmentClosed("test-segment")

            // When
            val result = reducer.reduce(stateWithCurrent, event)

            // Then
            // 当前段应该被移除
            assertNull(result.state.current)

            // 段应该被加入队列并标记为已闭合
            assertEquals(1, result.state.queue.size)
            val queuedSegment = result.state.queue.first()
            assertEquals("test-segment", queuedSegment.id)
            assertTrue(queuedSegment.closed)
        }

        @Test
        @DisplayName("UiSegmentRendered应该从队列移除段")
        fun `UiSegmentRendered should remove segment from queue`() {
            // Given
            val segment = Segment(
                id = "rendered-segment",
                kind = SegmentKind.PERTHINK,
                title = "Test",
                content = "content",
                closed = true,
                rendered = false
            )
            val stateWithQueue = initialState.copy(
                queue = ArrayDeque<Segment>(listOf(segment))
            )
            val event = ThinkingEvent.UiSegmentRendered("rendered-segment")

            // When
            val result = reducer.reduce(stateWithQueue, event)

            // Then
            assertTrue(result.state.queue.isEmpty())
        }
    }

    @Nested
    @DisplayName("ThinkingClosed事件测试")
    inner class ThinkingClosedTests {

        @Test
        @DisplayName("ThinkingClosed应该设置thinkingClosed标志")
        fun `ThinkingClosed should set thinkingClosed flag`() {
            // Given
            val event = ThinkingEvent.ThinkingClosed

            // When
            val result = reducer.reduce(initialState, event)

            // Then
            assertTrue(result.state.thinkingClosed)
        }

        @Test
        @DisplayName("ThinkingClosed应该生成NotifyHistoryThinking Effect")
        fun `ThinkingClosed should generate NotifyHistoryThinking effect`() {
            // Given
            val stateWithContent = initialState.copy(
                queue = ArrayDeque<Segment>(listOf(
                    Segment(
                        id = "segment1",
                        kind = SegmentKind.PERTHINK,
                        title = "Test",
                        content = "thinking content",
                        closed = true,
                        rendered = false
                    )
                ))
            )
            val event = ThinkingEvent.ThinkingClosed

            // When
            val result = reducer.reduce(stateWithContent, event)

            // Then
            assertEquals(1, result.effects.size)
            val effect = result.effects[0] as ThinkingBoxContract.Effect.NotifyHistoryThinking
            assertEquals("test-message", effect.messageId)
            assertTrue(effect.thinkingMarkdown.contains("thinking content"))
        }
    }

    @Nested
    @DisplayName("Final内容处理测试")
    inner class FinalContentTests {

        @Test
        @DisplayName("FinalStart应该激活finalBuffer")
        fun `FinalStart should activate finalBuffer`() {
            // Given
            val event = ThinkingEvent.FinalStart

            // When
            val result = reducer.reduce(initialState, event)

            // Then
            // finalBuffer应该被初始化（通过后续的FinalContent事件验证）
            assertEquals(initialState.version + 1, result.state.version)
        }

        @Test
        @DisplayName("FinalContent应该追加到finalBuffer")
        fun `FinalContent should append to finalBuffer`() {
            // Given
            val event1 = ThinkingEvent.FinalContent("第一部分")
            val event2 = ThinkingEvent.FinalContent("第二部分")

            // When
            val result1 = reducer.reduce(initialState, event1)
            val result2 = reducer.reduce(result1.state, event2)

            // Then
            assertEquals("第一部分第二部分", result2.state.getFinalContent())
        }

        @Test
        @DisplayName("FinalComplete应该生成NotifyHistoryFinal Effect")
        fun `FinalComplete should generate NotifyHistoryFinal effect`() {
            // Given
            val stateWithFinal = initialState.copy()
            stateWithFinal.finalBuffer.append("最终答案内容")
            val event = ThinkingEvent.FinalComplete

            // When
            val result = reducer.reduce(stateWithFinal, event)

            // Then
            assertTrue(result.state.finalClosed)
            // 注意：streaming属性已被删除，现在是Flow-derived

            assertEquals(1, result.effects.size)
            val effect = result.effects[0] as ThinkingBoxContract.Effect.NotifyHistoryFinal
            assertEquals("test-message", effect.messageId)
            assertEquals("最终答案内容", effect.finalMarkdown)
        }
    }

    @Nested
    @DisplayName("状态查询方法测试")
    inner class StateQueryTests {

        @Test
        @DisplayName("getNextSegmentToRender应该返回队列头段")
        fun `getNextSegmentToRender should return queue head segment`() {
            // Given
            val segment1 = Segment(
                id = "segment1",
                kind = SegmentKind.PERTHINK,
                title = "First",
                content = "content1",
                closed = true,
                rendered = false
            )
            val segment2 = Segment(
                id = "segment2",
                kind = SegmentKind.PHASE,
                title = "Second",
                content = "content2",
                closed = true,
                rendered = false
            )
            val stateWithQueue = initialState.copy(
                queue = ArrayDeque<Segment>(listOf(segment1, segment2))
            )

            // When
            val result = stateWithQueue.getNextSegmentToRender()

            // Then
            assertNotNull(result)
            assertEquals("segment1", result!!.id)
        }

        @Test
        @DisplayName("getNextSegmentToRender在空队列时应该返回null")
        fun `getNextSegmentToRender should return null for empty queue`() {
            // Given
            val emptyQueueState = initialState.copy(queue = ArrayDeque<Segment>())

            // When
            val result = emptyQueueState.getNextSegmentToRender()

            // Then
            assertNull(result)
        }

        @Test
        @DisplayName("isFinalReadyToRender应该正确判断final状态")
        fun `isFinalReadyToRender should correctly determine final readiness`() {
            // Given - isFinalReadyToRender需要: thinkingClosed=true && queue.isEmpty() && finalBuffer.isNotEmpty()
            val stateNotReady = initialState.copy(
                thinkingClosed = false,
                finalClosed = false
            )
            val stateReady = initialState.copy(
                thinkingClosed = true,
                finalClosed = true,
                queue = ArrayDeque() // 确保队列为空
            ).apply {
                finalBuffer.append("some content") // 确保finalBuffer不为空
            }

            // When & Then
            assertFalse(stateNotReady.isFinalReadyToRender())
            assertTrue(stateReady.isFinalReadyToRender())
        }

        @Test
        @DisplayName("getFinalContent应该返回finalBuffer内容")
        fun `getFinalContent should return finalBuffer content`() {
            // Given
            val stateWithFinal = initialState.copy()
            stateWithFinal.finalBuffer.append("测试最终内容")

            // When
            val result = stateWithFinal.getFinalContent()

            // Then
            assertEquals("测试最终内容", result)
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    inner class EdgeCaseTests {

        @Test
        @DisplayName("对不存在段的SegmentClosed应该被忽略")
        fun `SegmentClosed for non-existent segment should be ignored`() {
            // Given
            val event = ThinkingEvent.SegmentClosed("non-existent")

            // When
            val result = reducer.reduce(initialState, event)

            // Then - 当段ID不匹配时，状态不变（包括version）
            assertEquals(initialState.version, result.state.version) // version不变
            assertTrue(result.state.queue.isEmpty())
            assertNull(result.state.current)
        }

        @Test
        @DisplayName("对不存在段的UiSegmentRendered应该被忽略")
        fun `UiSegmentRendered for non-existent segment should be ignored`() {
            // Given
            val event = ThinkingEvent.UiSegmentRendered("non-existent")

            // When
            val result = reducer.reduce(initialState, event)

            // Then
            assertEquals(initialState.version + 1, result.state.version)
            assertTrue(result.state.queue.isEmpty())
        }
    }
}
