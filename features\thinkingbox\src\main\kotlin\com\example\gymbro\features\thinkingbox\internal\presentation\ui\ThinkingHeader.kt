package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.*
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.designSystem.components.extras.rememberMetallicBrush
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.MapleMono
import com.example.gymbro.designSystem.theme.motion.MotionDurations

/**
 * ThinkingHeader - 724方案修复：符合双时序架构的Header组件
 *
 * 🔥 【724方案核心要求】：
 * - Header负责用户发送消息后即刻展现，减少用户等待
 * - 等待perthink开始后就会渐隐消失让位
 * - 符合finalmermaid大纲.md双时序架构要求
 *
 * 🎯 **显示时机控制**：
 * - 步骤1：用户发送消息后立即显示（isStreaming = true）
 * - 步骤2：收到perthink内容时继续显示但开始准备退出
 * - 步骤3：收到第一个正式Phase时渐隐消失
 * - 步骤4：有最终内容时完全隐藏
 */
@Composable
fun ThinkingHeader(
    title: String,
    isStreaming: Boolean = false,
    hasContent: Boolean = false,
    hasPreThinking: Boolean = false,
    modifier: Modifier = Modifier,
) {
    // 🔥 【简化逻辑】Header职责很简单：
    // 1. 用户发送消息后立即显示（isStreaming = true）
    // 2. 思考框开始渲染了就会消失（hasContent = true）
    // 就这么简单，不需要复杂的final、phase判断
    val shouldShowHeader = isStreaming && !hasContent

    AnimatedVisibility(
        visible = shouldShowHeader,
        enter = fadeIn(
            animationSpec = tween(
                durationMillis = MotionDurations.M, // Standard medium duration
                easing = FastOutSlowInEasing,
            ),
        ) + slideInVertically(
            animationSpec = tween(
                durationMillis = MotionDurations.M,
                easing = FastOutSlowInEasing,
            ),
            initialOffsetY = { -it / 2 }, // 从上方滑入
        ),
        exit = fadeOut(
            animationSpec = tween(
                durationMillis = MotionDurations.M,
                easing = FastOutSlowInEasing,
            ),
        ) + slideOutVertically(
            animationSpec = tween(
                durationMillis = MotionDurations.M,
                easing = FastOutSlowInEasing,
            ),
            targetOffsetY = { -it / 2 }, // 向上方滑出
        ),
        modifier = modifier,
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = Tokens.Spacing.Medium),
            contentAlignment = Alignment.CenterStart,
        ) {
            // 🔥 【简化显示逻辑】根据状态显示对应内容
            if (hasPreThinking) {
                // PreThinking模式 - 显示实际的perthink内容
                val preThinkingBrush = rememberMetallicBrush(
                    useAnimate = true,
                    rotationDurationMillis = 3000, // Keep this as is for specific brush animation
                    useHdr = false,
                )

                Text(
                    text = title,
                    style = androidx.compose.ui.text.TextStyle(
                        fontFamily = MapleMono,  // 🔥 【字体系统修复】使用项目标准字体族
                        fontSize = Tokens.Typography.Headline,
                        fontWeight = FontWeight.Medium,
                        fontStyle = FontStyle.Normal,
                        color = ColorTokens.Dark.OnSurface
                    ).copy(
                        brush = preThinkingBrush,
                    ),
                    modifier = Modifier.graphicsLayer {
                        alpha = 0.7f
                    },
                )
            } else {
                // 初始loading状态 - 显示"thinking"动画
                val metallicBrush = rememberMetallicBrush(
                    useAnimate = isStreaming,
                    rotationDurationMillis = 2000, // Keep this as is for specific brush animation
                    useHdr = false,
                )

                Text(
                    text = title,
                    style = androidx.compose.ui.text.TextStyle(
                        fontFamily = MapleMono,  // 🔥 【字体系统修复】使用项目标准字体族
                        fontSize = Tokens.Typography.Headline,
                        fontWeight = FontWeight.SemiBold,
                        color = ColorTokens.Dark.OnSurface
                    ).copy(
                        brush = metallicBrush,
                    ),
                    modifier = Modifier.graphicsLayer {
                        alpha = if (isStreaming) 1.0f else 0.7f
                    },
                )
            }
        }
    }
}
