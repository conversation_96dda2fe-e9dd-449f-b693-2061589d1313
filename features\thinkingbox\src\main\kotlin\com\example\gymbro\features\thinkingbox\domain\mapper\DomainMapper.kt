package com.example.gymbro.features.thinkingbox.domain.mapper

import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DomainMapper - Segment队列架构映射器
 *
 * 🎯 核心职责（729方案3.md）：
 * - 将 StreamingThinkingMLParser 产生的 SemanticEvent 转换为 ThinkingEvent
 * - 实现三断点规则映射：<thinking>、</phase>、</thinking>
 * - 管理Segment生命周期：创建→填充→闭合
 * - 精简状态管理，移除复杂布尔标志
 *
 * 🔥 架构原则：
 * - 纯映射器：无副作用，只进行事件转换
 * - 精简上下文：只保留当前段信息和标题缓冲
 * - 断点驱动：基于三个关键断点进行状态转换
 */
@Singleton
class DomainMapper @Inject constructor() {

    /**
     * 映射上下文 - 精简版状态容器（729方案3.md）
     * 移除复杂状态标志，只保留核心字段
     */
    data class MappingContext(
        val currentSegmentId: String? = null, // 当前写入中的段ID
        val currentKind: SegmentKind? = null, // 当前段类型
        val inFinal: Boolean = false, // 是否在<final>标签内（用于文本分发）
        // 🔥 【729方案9优化】使用MutableInt避免多处copy()分配
        val phaseIdCounter: MutableInt = MutableInt(1), // 自动递增的phase ID计数器
        // 🔥 【关键修复】添加状态机管理，严格按照finalmermaid规范
        val parseState: ParsePhase = ParsePhase.PRE_THINK,
    ) {
        /**
         * 🔥 【729方案9优化】可变整数类，避免频繁copy()
         */
        data class MutableInt(var value: Int) {
            fun increment(): Int = ++value
            fun get(): Int = value
        }
    }

    /**
     * 解析阶段状态机 - 严格按照finalmermaid规范
     */
    enum class ParsePhase {
        PRE_THINK,      // perthink阶段，只能被<thinking>终止
        FORMAL_PHASE,   // 正式phase阶段，<phase>和</phase>在此阶段工作
        FINAL_PHASE     // </thinking>后的final阶段
    }

    /**
     * 映射结果
     * 包含转换后的业务事件和更新的上下文
     */
    data class MappingResult(
        val events: List<ThinkingEvent>,
        val context: MappingContext,
    )

    /**
     * 主映射方法 - 基于三断点规则的Segment映射（729方案3.md）
     *
     * @param event 输入的语义事件
     * @param context 当前映射上下文
     * @return 映射结果，包含转换后的事件和更新的上下文
     */
    fun mapSemanticToThinking(
        event: SemanticEvent,
        context: MappingContext = MappingContext(),
    ): MappingResult {
        // 记录关键事件的映射
        if (isKeyEvent(event)) {
            Timber.tag("TB-MAPPER").d("🔥 [Segment映射] 处理关键事件: ${event::class.simpleName}")
        }

        return when (event) {
            // ===== Segment队列架构映射（729方案3.md）=====

            is SemanticEvent.TagOpened -> mapTagOpened(event, context)
            is SemanticEvent.TagClosed -> mapTagClosed(event, context)
            is SemanticEvent.TextChunk -> mapTextChunk(event, context)

            // ===== 直接映射的Final事件 =====

            is SemanticEvent.FinalStart -> {
                Timber.tag("TB-MAPPER").d("🔥 <final> 标签开始 - 激活finalBuffer")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context.copy(inFinal = true),
                )
            }

            is SemanticEvent.FinalChunk -> {
                MappingResult(
                    events = listOf(ThinkingEvent.FinalContent(event.content)),
                    context = context,
                )
            }

            is SemanticEvent.FinalEnd -> {
                Timber.tag("TB-MAPPER").d("🔥 </final> 标签结束 - 触发History写入")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalComplete),
                    context = context.copy(inFinal = false),
                )
            }

            // ===== 不需要映射的事件 =====

            is SemanticEvent.RawThinking,
            is SemanticEvent.RawThinkingChunk,
            is SemanticEvent.RawThinkingClosed,
            is SemanticEvent.StreamFinished,
            is SemanticEvent.FunctionCallDetected,
            is SemanticEvent.TokensSnapshot,
            is SemanticEvent.ParseErrorEvent,
            -> {
                // 这些事件不需要映射到ThinkingEvent
                MappingResult(events = emptyList(), context = context)
            }

            // ===== 旧事件（已弃用，保留兼容性）=====

            is SemanticEvent.PreThinkChunk,
            is SemanticEvent.PreThinkEnd,
            is SemanticEvent.PhaseStart,
            is SemanticEvent.PhaseContent,
            is SemanticEvent.PhaseEnd,
            is SemanticEvent.PhaseTitleUpdate,
            -> {
                // 旧事件已弃用，不再映射到新的ThinkingEvent
                Timber.tag("TB-MAPPER").w("⚠️ [弃用事件] 忽略旧事件: ${event::class.simpleName}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理XML标签开启事件 - 基于三断点规则（729方案3.md）
     * 实现Segment创建和状态转换
     */
    private fun mapTagOpened(
        event: SemanticEvent.TagOpened,
        context: MappingContext,
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                // 创建PERTHINK段
                Timber.tag("TB-MAPPER").d("🎯 <think> 标签 - 创建PERTHINK段")
                MappingResult(
                    events = listOf(
                        ThinkingEvent.SegmentStarted("perthink", SegmentKind.PERTHINK, "Bro is thinking"),
                    ),
                    context = context.copy(
                        currentSegmentId = "perthink",
                        currentKind = SegmentKind.PERTHINK,
                    ),
                )
            }

            "thinking" -> {
                // 🔥 【finalmermaid规范】断点规则1：<thinking> 结束perthink段并转换到FORMAL_PHASE状态
                Timber.tag("TB-MAPPER").i("🔚 [finalmermaid规范] <thinking> 断点 - 结束perthink段，进入正式phase阶段")
                val events = mutableListOf<ThinkingEvent>()

                // 只在PRE_THINK状态下处理perthink段闭合
                if (context.parseState == ParsePhase.PRE_THINK) {
                    if (context.currentSegmentId != null) {
                        events.add(ThinkingEvent.SegmentClosed(context.currentSegmentId!!))
                        Timber.tag("TB-MAPPER").d("✅ [finalmermaid规范] 闭合perthink段: ${context.currentSegmentId}")
                    } else {
                        // 兜底机制：确保perthink段被闭合
                        events.add(ThinkingEvent.SegmentClosed("perthink"))
                        Timber.tag("TB-MAPPER").w("⚠️ [兜底机制] 强制闭合perthink段")
                    }
                } else {
                    Timber.tag("TB-MAPPER").w("⚠️ [状态错误] <thinking>标签在非PRE_THINK状态下出现: ${context.parseState}")
                }

                MappingResult(
                    events = events,
                    context = context.copy(
                        currentSegmentId = null,
                        currentKind = null,
                        // 🔥 【关键修复】转换到FORMAL_PHASE状态
                        parseState = ParsePhase.FORMAL_PHASE,
                    ),
                )
            }

            "phase" -> {
                // 🔥 【finalmermaid规范】<phase>标签只在FORMAL_PHASE状态下工作
                when (context.parseState) {
                    ParsePhase.PRE_THINK -> {
                        // 🔥 【729方案9强化】在PRE_THINK状态下，<phase>标签直接丢弃并上报ParseError
                        Timber.tag("TB-MAPPER").e("❌ [finalmermaid规范] <phase>标签在PRE_THINK状态下被丢弃，需要先<thinking>")
                        MappingResult(
                            events = listOf(
                                ThinkingEvent.ParseError("Invalid <phase> tag in PRE_THINK state. Must use <thinking> first.")
                            ),
                            context = context
                        )
                    }
                    ParsePhase.FORMAL_PHASE -> {
                        // 🔥 【正确逻辑】只在FORMAL_PHASE状态下处理<phase>标签
                        val phaseId = event.attrs["id"] ?: "phase-${context.phaseIdCounter.get()}"
                        val events = mutableListOf<ThinkingEvent>()

                        // 先结束当前段（如果有）
                        if (context.currentSegmentId != null) {
                            Timber.tag("TB-MAPPER").i("🔄 [finalmermaid规范] 新phase: $phaseId - 结束当前段: ${context.currentSegmentId}")
                            events.add(ThinkingEvent.SegmentClosed(context.currentSegmentId!!))
                        }

                        // 创建新的PHASE段
                        Timber.tag("TB-MAPPER").i("🎯 [finalmermaid规范] 创建正式phase段: $phaseId")
                        events.add(ThinkingEvent.SegmentStarted(phaseId, SegmentKind.PHASE, null))

                        // 🔥 【729方案9优化】如果没有显式ID，自动递增计数器
                        if (event.attrs["id"] == null) {
                            context.phaseIdCounter.increment()
                        }

                        MappingResult(
                            events = events,
                            context = context.copy(
                                currentSegmentId = phaseId,
                                currentKind = SegmentKind.PHASE,
                            ),
                        )
                    }
                    ParsePhase.FINAL_PHASE -> {
                        Timber.tag("TB-MAPPER").w("⚠️ [finalmermaid规范] <phase>标签在FINAL_PHASE状态下被忽略")
                        MappingResult(events = emptyList(), context = context)
                    }
                }
            }

            "final" -> {
                // 激活final模式
                Timber.tag("TB-MAPPER").d("🔥 <final> 标签开始 - 激活finalBuffer")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalStart),
                    context = context.copy(inFinal = true),
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理XML标签关闭事件 - 基于三断点规则（729方案3.md）
     * 实现Segment闭合和特殊断点处理
     */
    private fun mapTagClosed(
        event: SemanticEvent.TagClosed,
        context: MappingContext,
    ): MappingResult {
        return when (event.name) {
            "think" -> {
                // </think>标签结束，但不闭合段（等待<thinking>断点）
                Timber.tag("TB-MAPPER").d("🔚 </think> 标签结束")
                MappingResult(events = emptyList(), context = context)
            }

            "thinking" -> {
                // 🔥 【finalmermaid规范】断点规则3：</thinking> 创建final-phase段并触发ThinkingClosed
                Timber.tag("TB-MAPPER").d("🔚 [finalmermaid规范] </thinking> 断点 - 结束思考阶段")
                val events = mutableListOf<ThinkingEvent>()

                // 🔥 【729方案9强化】自动补发未闭合段的SegmentClosed
                if (context.currentSegmentId != null) {
                    events.add(ThinkingEvent.SegmentClosed(context.currentSegmentId!!))
                    Timber.tag("TB-MAPPER").d("✅ [finalmermaid规范] 闭合当前段: ${context.currentSegmentId}")
                } else {
                    // 🔥 【兜底机制】如果没有当前段，检查是否有遗漏的段需要闭合
                    Timber.tag("TB-MAPPER").w("⚠️ [兜底机制] </thinking>时无当前段，可能存在未闭合的段")
                }

                // 🔥 【finalmermaid规范】创建final-phase段（用于关闭思考框）
                events.add(ThinkingEvent.SegmentStarted("final-phase", SegmentKind.FINAL_PHASE, "Final Answer"))
                events.add(ThinkingEvent.SegmentClosed("final-phase")) // 立即闭合，触发UI关闭

                // 🔥 【finalmermaid规范】发送思考阶段结束事件，触发History写入
                events.add(ThinkingEvent.ThinkingClosed)

                Timber.tag("TB-MAPPER").d("✅ [finalmermaid规范] 思考阶段结束，创建final-phase段")

                MappingResult(
                    events = events,
                    context = context.copy(
                        currentSegmentId = null,
                        currentKind = null,
                        // 🔥 【关键修复】转换到FINAL_PHASE状态
                        parseState = ParsePhase.FINAL_PHASE,
                    ),
                )
            }

            "phase" -> {
                // 🔥 【finalmermaid规范】</phase>只在FORMAL_PHASE状态下关闭段
                when (context.parseState) {
                    ParsePhase.PRE_THINK -> {
                        // 🔥 【关键修复】在PRE_THINK状态下，</phase>标签被忽略，不能关闭perthink段
                        Timber.tag("TB-MAPPER").w("⚠️ [finalmermaid规范] </phase>标签在PRE_THINK状态下被忽略，perthink段只能被<thinking>关闭")
                        MappingResult(events = emptyList(), context = context)
                    }
                    ParsePhase.FORMAL_PHASE -> {
                        // 🔥 【正确逻辑】只在FORMAL_PHASE状态下关闭正式phase段
                        val segmentId = context.currentSegmentId
                        if (segmentId != null && context.currentKind == SegmentKind.PHASE) {
                            Timber.tag("TB-MAPPER").i("🔚 [finalmermaid规范] </phase> 断点 - 闭合正式phase段: $segmentId")
                            MappingResult(
                                events = listOf(ThinkingEvent.SegmentClosed(segmentId)),
                                context = context.copy(
                                    currentSegmentId = null,
                                    currentKind = null,
                                ),
                            )
                        } else {
                            Timber.tag("TB-MAPPER").w("⚠️ [finalmermaid规范] </phase>标签但无当前PHASE段")
                            MappingResult(events = emptyList(), context = context)
                        }
                    }
                    ParsePhase.FINAL_PHASE -> {
                        Timber.tag("TB-MAPPER").w("⚠️ [finalmermaid规范] </phase>标签在FINAL_PHASE状态下被忽略")
                        MappingResult(events = emptyList(), context = context)
                    }
                }
            }

            "final" -> {
                // </final>标签结束，触发最终内容完成
                Timber.tag("TB-MAPPER").d("🔥 </final> 标签 - 触发History写入")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalComplete),
                    context = context.copy(inFinal = false),
                )
            }

            else -> {
                Timber.tag("TB-MAPPER").w("未知标签关闭: ${event.name}")
                MappingResult(events = emptyList(), context = context)
            }
        }
    }

    /**
     * 处理文本块事件 - 基于Segment模型的文本分发（729方案3.md）
     * 根据当前上下文状态将文本分发到对应的Segment或finalBuffer
     */
    private fun mapTextChunk(
        event: SemanticEvent.TextChunk,
        context: MappingContext,
    ): MappingResult {
        return when {
            // final标签内的文本 - 直接发送到finalBuffer
            context.inFinal -> {
                Timber.tag("TB-MAPPER").d("� Final文本: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.FinalContent(event.text)),
                    context = context,
                )
            }

            // 当前段内的文本 - 发送到当前段
            context.currentSegmentId != null -> {
                Timber.tag("TB-MAPPER").d("📝 段[${context.currentSegmentId}]文本: ${event.text.take(50)}...")
                MappingResult(
                    events = listOf(ThinkingEvent.SegmentText(event.text)),
                    context = context,
                )
            }

            else -> {
                // 🔥 【finalmermaid规范】根据ParsePhase状态决定如何处理文本
                when (context.parseState) {
                    ParsePhase.PRE_THINK -> {
                        // 在PRE_THINK状态下，自动创建perthink段
                        if (event.text.trim().isNotEmpty()) {
                            Timber.tag("TB-MAPPER").i("📝 [finalmermaid规范] PRE_THINK状态，自动创建perthink段")
                            val events = listOf(
                                ThinkingEvent.SegmentStarted("perthink", SegmentKind.PERTHINK, "Bro is thinking"),
                                ThinkingEvent.SegmentText(event.text)
                            )
                            MappingResult(
                                events = events,
                                context = context.copy(
                                    currentSegmentId = "perthink",
                                    currentKind = SegmentKind.PERTHINK,
                                ),
                            )
                        } else {
                            MappingResult(events = emptyList(), context = context)
                        }
                    }
                    ParsePhase.FORMAL_PHASE -> {
                        // 在FORMAL_PHASE状态下，文本应该在<phase>标签内
                        if (event.text.trim().isNotEmpty()) {
                            Timber.tag("TB-MAPPER").w("⚠️ [finalmermaid规范] FORMAL_PHASE状态下的裸文本被忽略，应该在<phase>标签内")
                        }
                        MappingResult(events = emptyList(), context = context)
                    }
                    ParsePhase.FINAL_PHASE -> {
                        // 在FINAL_PHASE状态下，文本被忽略
                        if (event.text.trim().isNotEmpty()) {
                            Timber.tag("TB-MAPPER").w("⚠️ [finalmermaid规范] FINAL_PHASE状态下的文本被忽略")
                        }
                        MappingResult(events = emptyList(), context = context)
                    }
                }
            }
        }
    }



    /**
     * 判断是否为关键事件（需要特别记录日志）
     * 更新为Segment架构的关键事件
     */
    private fun isKeyEvent(event: SemanticEvent): Boolean {
        return when (event) {
            is SemanticEvent.TagOpened -> event.name in listOf("think", "thinking", "phase", "final")
            is SemanticEvent.TagClosed -> event.name in listOf("thinking", "phase", "final")
            is SemanticEvent.FinalStart,
            is SemanticEvent.FinalEnd,
            -> true
            else -> false
        }
    }
}
