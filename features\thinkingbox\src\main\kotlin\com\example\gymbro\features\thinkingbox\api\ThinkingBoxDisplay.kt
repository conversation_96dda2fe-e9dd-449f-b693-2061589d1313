package com.example.gymbro.features.thinkingbox.api

/**
 * ThinkingBox显示接口
 * 
 * 用于Coach模块启动和控制ThinkingBox的显示过程。
 * 遵循简化的职责分离原则：Coach负责业务逻辑，ThinkingBox负责显示。
 * 
 * @since Coach-ThinkingBox重构
 */
interface ThinkingBoxDisplay {
    
    /**
     * 启动ThinkingBox显示AI响应
     * 
     * @param messageId 消息ID，用于关联显示会话
     * @param completionListener 完成回调监听器
     */
    fun startDisplaying(
        messageId: String,
        completionListener: ThinkingBoxCompletionListener
    )
    
    /**
     * 停止正在进行的显示过程
     * 
     * @param messageId 消息ID
     */
    fun stopDisplaying(messageId: String)
}
