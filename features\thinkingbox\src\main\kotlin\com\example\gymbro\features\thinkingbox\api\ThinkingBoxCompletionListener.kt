package com.example.gymbro.features.thinkingbox.api

/**
 * ThinkingBox完成回调接口
 * 
 * 当ThinkingBox完成AI响应显示时，通过此接口回调Coach模块进行结果保存。
 * 遵循简化的职责分离原则：ThinkingBox完成显示后通知Coach保存结果。
 * 
 * @since Coach-ThinkingBox重构
 */
interface ThinkingBoxCompletionListener {
    
    /**
     * ThinkingBox显示完成回调
     * 
     * @param messageId 消息ID
     * @param thinkingProcess 思考过程的完整内容
     * @param finalContent 最终的AI响应内容
     * @param metadata 额外的元数据（如处理时间、token数量等）
     */
    fun onDisplayComplete(
        messageId: String,
        thinkingProcess: String,
        finalContent: String,
        metadata: Map<String, Any> = emptyMap()
    )
    
    /**
     * ThinkingBox显示失败回调
     * 
     * @param messageId 消息ID
     * @param error 错误信息
     * @param partialResult 部分结果（如果有）
     */
    fun onDisplayError(
        messageId: String,
        error: Throwable,
        partialResult: String? = null
    )
}
