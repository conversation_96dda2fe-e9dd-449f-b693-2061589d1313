package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import androidx.compose.material3.Text
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import kotlinx.coroutines.delay
import timber.log.Timber

/**
 * StreamingFinalRenderer - 流式打字机效果最终内容渲染器
 *
 * 🎯 **流式打字机架构**：
 * - 即时响应：一有finalTokens就立即开始打字机渲染，不等待</final>标签
 * - 分阶段渲染：基于chunk进行渲染，避免用户等待时间过长
 * - 逐字显示：按100ms间隔逐个显示tokens，实现真正的流式体验
 * - 无缝继续：新tokens到达时，从上次位置继续打字机效果
 *
 * 🔥 **核心优势**：
 * - 用户体验优先：消除等待</final>标签的延迟
 * - 流式响应：tokens流式到达，UI流式渲染
 * - 性能优化：避免重复渲染和不必要的等待
 * - 进度可见：用户可以实时看到内容逐步显示
 *
 * @param finalTokens 最终token列表（流式到达，实时更新）
 * @param isFinalStreaming 是否正在流式传输（仅用于完成判断）
 * @param tokenizerService Token计算服务（可选）
 * @param onRenderingComplete 渲染完成回调
 * @param modifier Compose修饰符
 */
@Composable
fun StreamingFinalRenderer(
    finalTokens: List<String>,
    isFinalStreaming: Boolean,
    tokenizerService: TokenizerService? = null,
    onRenderingComplete: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
) {
    // 🔥 【最终内容渲染】只在流式结束且有内容时显示
    var renderingCompleted by remember { mutableStateOf(false) }

    // 🔥 【打字机效果状态】跟踪已显示的tokens数量
    var displayedTokensCount by remember { mutableStateOf(0) }

    // 🔥 【防重复渲染】实例唯一性标记
    val rendererId = remember { "final-renderer-${System.currentTimeMillis()}" }

    LaunchedEffect(rendererId) {
        Timber.tag("TB-FINAL-RENDERER").w("🔥 [最终渲染器] 实例创建: id=$rendererId, tokens=${finalTokens.size}")
    }

    // 🔥 【流式完成检测】当所有tokens都显示完且流式结束时才算完成
    LaunchedEffect(finalTokens.size, displayedTokensCount, isFinalStreaming) {
        val isAllTokensDisplayed = displayedTokensCount >= finalTokens.size
        val isStreamingEnded = !isFinalStreaming

        if (isAllTokensDisplayed && isStreamingEnded && finalTokens.isNotEmpty() && !renderingCompleted) {
            // 短暂延迟确保最后一个token显示完成
            delay(100)
            renderingCompleted = true
            onRenderingComplete?.invoke()
            Timber.tag("TB-FINAL-RENDERER").i("🎉 [流式打字机] 完成渲染，共显示${finalTokens.size}个tokens")
        }
    }

    // 🔥 【流式打字机修复】一有tokens就立即开始打字机渲染，不等待</final>标签
    if (finalTokens.isNotEmpty()) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = Tokens.Spacing.None), // Use tokens instead of hardcoded 0.dp
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
        ) {
            // 🔥 【流式打字机核心】响应tokens变化，实现分阶段渲染
            LaunchedEffect(finalTokens.size) {
                if (finalTokens.size > displayedTokensCount) {
                    Timber.tag(
                        "StreamingFinalRenderer",
                    ).d("🎬 [流式打字机] 新tokens到达: $displayedTokensCount → ${finalTokens.size}")
                    // 从已显示位置继续渲染新到达的tokens
                    for (i in displayedTokensCount until finalTokens.size) {
                        displayedTokensCount = i + 1
                        delay(33) // 🔥 打字机间隔：33ms，实现每秒30个字符的渲染速度
                    }
                    Timber.tag(
                        "StreamingFinalRenderer",
                    ).d("🎬 [流式打字机] 当前阶段完成: 已显示$displayedTokensCount/${finalTokens.size}")
                }
            }

            // 🔥 【内容显示】显示已打字的内容
            val displayedContent = finalTokens.take(displayedTokensCount).joinToString("")

            if (displayedContent.isNotBlank()) {
                // 🔥 【Markdown渲染】使用已显示的内容进行正确的markdown渲染
                Text(
                    text = displayedContent,
                    style = androidx.compose.ui.text.TextStyle(
                        fontSize = Tokens.Typography.BodyMedium,
                        color = ColorTokens.Dark.OnSurface
                    ),
                    modifier = Modifier.fillMaxWidth(),
                )
            }

            // Token计数器（渲染完成后显示）
            val isRenderingComplete = !isFinalStreaming && displayedTokensCount >= finalTokens.size
            if (isRenderingComplete && tokenizerService != null) {
                AnimatedVisibility(
                    visible = true,
                    enter = fadeIn(
                        animationSpec = tween(
                            durationMillis = MotionDurations.M, // Standard medium duration
                            delayMillis = 200,
                            easing = FastOutSlowInEasing,
                        ),
                    ),
                ) {
                    val tokenCount = remember(displayedContent, tokenizerService) {
                        try {
                            tokenizerService.countTokens(
                                displayedContent,
                                com.example.gymbro.core.ai.tokenizer.ModelTypes.GPT_4,
                            )
                        } catch (e: Exception) {
                            (displayedContent.length + 3) / 4
                        }
                    }

                    Text(
                        text = "~$tokenCount tokens",
                        style = androidx.compose.ui.text.TextStyle(
                            fontSize = Tokens.Typography.Small, // 使用Small替代LabelSmall
                            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
                            color = ColorTokens.Dark.OnSurfaceVariant
                        ),
                        modifier = Modifier.padding(top = Tokens.Spacing.Small),
                    )
                }
            }
        }
    } else {
        // 🔥 【724方案修复】移除"正在接收内容"预设提示，空状态时不显示任何内容
        // 这样可以避免不必要的用户等待提示，让界面更加简洁
    }
}

// ==================== 最终渲染数据结构（简化版）====================

// 🔥 【代码清理】移除不再需要的流式渲染相关数据结构和函数
// 由于已改为最终内容一次性渲染，不再需要分段和流式处理逻辑

// ==================== Preview Functions ====================

/**
 * StreamingFinalRenderer Preview - 正在渲染状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun StreamingFinalRendererPreview_Streaming() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        StreamingFinalRenderer(
            finalTokens = listOf("根据", "你的", "需求，", "我", "推荐", "以下", "训练", "计划："),
            isFinalStreaming = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * StreamingFinalRenderer Preview - 完整内容
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun StreamingFinalRendererPreview_Complete() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        StreamingFinalRenderer(
            finalTokens = listOf(
                "根据", "你的", "需求，", "我", "推荐", "以下", "训练", "计划：", "\n\n",
                "**", "周一", "**：", "胸部", "训练", "\n",
                "- ", "卧推", " 4", "组", " x ", "8-12", "次", "\n",
                "- ", "哑铃", "飞鸟", " 3", "组", " x ", "10-15", "次", "\n\n",
                "**", "周三", "**：", "背部", "训练", "\n",
                "- ", "引体", "向上", " 4", "组", " x ", "6-10", "次", "\n",
                "- ", "划船", " 3", "组", " x ", "8-12", "次",
            ),
            isFinalStreaming = false,
            tokenizerService = null,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * StreamingFinalRenderer Preview - 空状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun StreamingFinalRendererPreview_Empty() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        StreamingFinalRenderer(
            finalTokens = emptyList(),
            isFinalStreaming = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * StreamingFinalRenderer Preview - 短文本
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun StreamingFinalRendererPreview_ShortText() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        StreamingFinalRenderer(
            finalTokens = listOf("好的，", "我", "明白", "了。"),
            isFinalStreaming = false,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}
