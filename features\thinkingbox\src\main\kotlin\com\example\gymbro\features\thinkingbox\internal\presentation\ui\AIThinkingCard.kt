package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.*
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import timber.log.Timber

@OptIn(ExperimentalAnimationApi::class)

/**
 * shouldShowAIThinkingCard - 判断ThinkingBox是否应该显示
 * 🔥 【基于实际情况】完全基于token接收的真实情况决定显示
 */
fun shouldShowAIThinkingCard(
    state: ThinkingBoxContract.State,
): Boolean {
    return when {
        // 有实际内容需要显示
        state.segmentsQueue.isNotEmpty() -> {
            timber.log.Timber.tag("TB-DISPLAY").d("✅ 显示条件满足: 有${state.segmentsQueue.size}个段需要显示")
            true
        }
        // 正在等待或处理中（显示ThinkingHeader）
        state.isStreaming() && !state.thinkingClosed -> {
            timber.log.Timber.tag("TB-DISPLAY").d("✅ 显示条件满足: 等待token或处理中")
            true
        }
        // 其他情况隐藏
        else -> {
            timber.log.Timber.tag("TB-DISPLAY").d("❌ 显示条件不满足: 队列=${state.segmentsQueue.size}, 流式=${state.isStreaming()}, 思考关闭=${state.thinkingClosed}")
            false
        }
    }
}

// 🔥 【重载冲突修复】SimpleSummaryText已移至ThinkingBoxExports.kt作为公共API
// 内部实现在SimpleSummaryTextInternal.kt中，避免重复定义

/**
 * 🔥 【架构简化】AIThinkingCard - 集成ThinkingHeader的思考卡片
 *
 * 核心功能：
 * - 🔥 【内部管理】ThinkingHeader和思考框的显示逻辑
 * - 🔥 【平滑过渡】ThinkingHeader到思考框的动画过渡
 * - 🔥 【队列渲染】严格按照Segment队列顺序渲染
 * - 实现段渲染完成回调机制
 *
 * @param state ThinkingBox Contract State
 * @param messageId 消息ID
 * @param modifier 修饰符
 * @param onSegmentRendered 段渲染完成回调
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun AIThinkingCard(
    state: ThinkingBoxContract.State,
    messageId: String,
    modifier: Modifier = Modifier,
    onSegmentRendered: ((String) -> Unit)? = null,
) {
    // 🔥 【P0修复】只渲染队列中的第一个段，移除currentSegment逻辑
    val segmentToRender = state.segmentsQueue.firstOrNull()

    // 🔥 【MVI规范】状态监听 - 监听关键状态变化
    LaunchedEffect(
        state.segmentsQueue.size,
        segmentToRender?.id,
        state.thinkingClosed,
        state.isStreaming(),
    ) {
        Timber.tag("TB-UI").d(
            "📊 Segment状态: 队列头段=${segmentToRender?.id}, 队列大小=${state.segmentsQueue.size}, " +
                "思考关闭=${state.thinkingClosed}, 流式=${state.isStreaming()}",
        )
    }

    // 段渲染完成回调
    val enhancedSegmentRendered: (String) -> Unit = remember(onSegmentRendered) {
        { segmentId ->
            Timber.tag("TB-UI").d("✅ 段渲染完成: $segmentId")
            onSegmentRendered?.invoke(segmentId)
        }
    }

    // 🔥 【基于实际情况】完全基于token接收的真实情况决定显示内容
    val hasRealContent = segmentToRender != null && segmentToRender.content.isNotEmpty()

    when {
        hasRealContent -> {
            // 收到真实token内容 → 显示思考框
            Timber.tag("TB-RENDER").d("🎯 显示思考内容: ${segmentToRender!!.id} (${segmentToRender.kind}), 内容=${segmentToRender.content.length}字符")

            // 🔥 【平滑过渡】ThinkingHeader到思考框的动画过渡
            androidx.compose.animation.AnimatedContent(
                targetState = segmentToRender,
                transitionSpec = {
                    androidx.compose.animation.fadeIn(
                        animationSpec = androidx.compose.animation.core.tween(
                            durationMillis = MotionDurations.M // Standard medium duration
                        )
                    ) with androidx.compose.animation.fadeOut(
                        animationSpec = androidx.compose.animation.core.tween(
                            durationMillis = MotionDurations.M // Standard medium duration
                        )
                    )
                },
                modifier = modifier.fillMaxWidth()
            ) { segment ->
                ThinkingStageCard(
                    segment = segment,
                    modifier = Modifier.fillMaxWidth(),
                    onSegmentRendered = enhancedSegmentRendered,
                )
            }
        }
        state.isStreaming() && !state.thinkingClosed -> {
            // 未收到真实token内容 → 显示等待状态
            Timber.tag("TB-RENDER").d("📱 显示等待状态: ThinkingHeader")
            com.example.gymbro.features.thinkingbox.internal.presentation.ui.ThinkingHeader(
                title = "Bro is thinking...",
                isStreaming = true,
                hasContent = false,
                modifier = modifier.fillMaxWidth()
            )
        }
        else -> {
            Timber.tag("TB-RENDER").d("❌ 无显示内容: 流式=${state.isStreaming()}, 思考关闭=${state.thinkingClosed}")
        }
    }
}
