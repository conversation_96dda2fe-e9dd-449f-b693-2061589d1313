package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.components.extras.rememberMetallicBrush
import com.example.gymbro.designSystem.components.text.ProgressiveTextRenderer
import com.example.gymbro.designSystem.components.text.RenderSpeed
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.MapleMono
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import kotlinx.coroutines.delay
import timber.log.Timber

/**
 * ThinkingStageCard - Segment队列架构版本（729方案4.md）
 *
 * 基于SegmentUi的思考段落卡片，支持Segment队列模型
 * 布局：title → 文本内容（无多余分割线和空间）
 * 🔥 【Segment架构】使用SegmentUi替代PhaseUi
 * 🔥 【UiSegmentRendered】动画完成后发送UiSegmentRendered事件
 */
@Composable
fun ThinkingStageCard(
    segment: ThinkingBoxContract.SegmentUi,
    isActive: Boolean = true,
    modifier: Modifier = Modifier,
    // 🔥 【UiSegmentRendered】动画完成回调，发送UiSegmentRendered(id)
    onSegmentRendered: ((String) -> Unit)? = null,
) {
    val scope = rememberCoroutineScope()
    // 1/3屏高度限制
    val maxHeight = (LocalConfiguration.current.screenHeightDp * 0.33f).dp

    Card(
        modifier =
        modifier
            .fillMaxWidth()
            .heightIn(max = maxHeight)
            .let {
                // 🔥 【动—静双层方案】卡片层：elevation + scale 动画
                AnimationEngine.run { it.phaseTransitionAnimation(isActive) }
            },
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        elevation = CardDefaults.cardElevation(Tokens.Elevation.Small),
        colors =
        CardDefaults.cardColors(
            containerColor = Tokens.Color.Gray100,
        ),
    ) {
        // 🔥 【修复phase卡住问题】移除AnimatedContent，避免双重动画导致phase切换过早触发
        // phase切换动画由AIThinkingCard统一管理，ThinkingStageCard只负责内容展示
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        ) {
            // 🔥 【Segment标题】基于SegmentUi的标题显示逻辑
            val displayTitle = when {
                segment.kind == SegmentKind.PERTHINK -> ThinkingBoxStrings.PERTHINK_TITLE // perthink固定标题
                !segment.title.isNullOrBlank() -> segment.title // 真实title
                else -> null // 无title时不显示header
            }

            // 🔥 【Segment调试】记录Segment显示逻辑
            Timber.tag("TB-SEGMENT-UI").d("🔥 [Segment显示] id=${segment.id}, kind=${segment.kind}")
            Timber.tag(
                "TB-SEGMENT-UI",
            ).d("🔥 [Segment显示] title='${segment.title}', displayTitle='$displayTitle'")

            // 🔥 【Title显示优化】只有在有title时才显示header，避免空白区域
            displayTitle?.let { title ->
                ChatGptThinkingHeader(
                    title = title,
                    segmentId = segment.id,
                    isActive = isActive,
                    isPerthink = segment.kind == SegmentKind.PERTHINK,
                )

                // 🔥 【1行间距】Header和内容间使用Small(8dp)间距，相当于1行间距
                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
            }

            // 🔥 【内容显示】Segment内容渲染
            if (segment.content.isNotEmpty()) {
                // 🔥 【729方案9优化】使用ProgressiveTextRenderer，立即绘制首段
                ProgressiveTextRenderer(
                    fullText = segment.content,
                    modifier = Modifier.fillMaxWidth(),
                    renderSpeed = RenderSpeed.INSTANT, // 🔥 【729方案9优化】所有段落都使用INSTANT速度，立即绘制
                    onComplete = {
                        // 🔥 【UiSegmentRendered】渲染完成后发送UiSegmentRendered事件
                        Timber.tag("TB-SEGMENT-UI").d(
                            "🔥 [Segment渲染完成] ${segment.kind} ${segment.id} 渲染完成，发送UiSegmentRendered",
                        )
                        onSegmentRendered?.invoke(segment.id)
                    },
                )
            }
        }
    }

    // 🔥 【UI改动回调修复】移除LaunchedEffect中的重复回调，只由打字机onDone触发
    // 双握手机制现在完全由UI动画完成（打字机onDone）触发，符合finalmermaid大纲要求
}

/**
 * ChatGPT风格思考Header组件 - Segment架构版本
 */
@Composable
private fun ChatGptThinkingHeader(
    title: String,
    segmentId: String,
    isActive: Boolean,
    isPerthink: Boolean = false,
) {
    // 🔥 【Segment调试】记录Header渲染
    Timber.tag(
        "TB-SEGMENT-UI",
    ).d("🔥 [Header渲染] title='$title', segmentId=$segmentId, isPerthink=$isPerthink")

    // 🔥 【724方案修复】移除StatusDot，只保留MetallicText标题
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 🔥 【724方案修复】标题使用金属字体效果，粗体，字号比正文大1号
        ChatGptMetallicText(
            text = title,
            segmentId = segmentId,
            isPerthink = isPerthink,
            modifier = Modifier.weight(1f),
        )
    }
}

/**
 * MetallicStreamingText - 流式金属质感文本渲染器
 *
 * 🎯 **核心架构**: 替换原有的typewriter动画系统，使用类似StreamingFinalRenderer的MetallicStreamingText效果
 * - 流式显示: 字符逐个显示，delay(100)实现打字机视觉效果
 * - 双时序支持: 支持dual timing sequence机制，确保动画回调接口正确匹配
 * - 金属质感: 保持原有的metallic brush效果
 * - 性能优化: 避免重复渲染和不必要的状态重置
 */
@Composable
private fun MetallicStreamingText(
    fullText: String,
    phaseId: String,
    isPreThink: Boolean = false,
    modifier: Modifier = Modifier,
    onDone: (() -> Unit)? = null,
) {
    // 🔥 【流式状态管理】跟踪已显示的字符数量
    var displayedCharsCount by remember(phaseId, fullText) { mutableStateOf(0) }
    var isStreamingActive by remember(phaseId, fullText) { mutableStateOf(false) }

    // 🔥 【MetallicStreaming核心】响应文本变化，实现流式字符显示
    LaunchedEffect(fullText.length, phaseId) {
        if (fullText.length > displayedCharsCount && !isStreamingActive) {
            isStreamingActive = true

            // 🔥 【双时序架构】根据phase类型调整streaming速度
            val charDelay = if (isPreThink) 50L else 100L // perthink更快，正式phase使用标准100ms

            // 🔥 【流式显示】从当前位置继续显示新字符
            for (i in displayedCharsCount until fullText.length) {
                displayedCharsCount = i + 1
                delay(charDelay) // 🔥 核心：使用delay(100)实现MetallicStreamingText效果
            }

            isStreamingActive = false

            // 🔥 【动画完成回调】支持双时序列机制
            if (displayedCharsCount >= fullText.length) {
                onDone?.invoke()
            }
        }
    }

    // 🔥 【金属质感】保持原有的metallic brush效果
    val metallicBrush = rememberMetallicBrush(
        useAnimate = true,
        rotationDurationMillis = if (isPreThink) 2000 else 3000,
        useHdr = false,
    )

    // 🔥 【内容显示】显示已流式显示的字符
    val displayedContent = fullText.take(displayedCharsCount)

    Text(
        text = displayedContent,
        style = androidx.compose.ui.text.TextStyle(
            fontFamily = MapleMono,  // 🔥 【字体系统修复】使用项目标准字体族
            fontSize = Tokens.Typography.Body
        ).copy(
            brush = metallicBrush,
        ),
        color = Tokens.Color.Gray800,
        modifier = modifier,
    )
}

/**
 * 金属质感标题文本 - Segment架构版本
 */
@Composable
private fun ChatGptMetallicText(
    text: String,
    segmentId: String,
    isPerthink: Boolean = false,
    modifier: Modifier = Modifier,
) {
    // 🔥 【流式状态管理】跟踪已显示的字符数量
    var displayedCharsCount by remember(segmentId, text) { mutableStateOf(0) }
    var isStreamingActive by remember(segmentId, text) { mutableStateOf(false) }

    // 🔥 【Title打字机效果】响应文本变化，实现流式字符显示
    LaunchedEffect(text.length, segmentId) {
        if (text.length > displayedCharsCount && !isStreamingActive) {
            isStreamingActive = true

            // 🔥 【统一动画速度】perthink和正式phase使用相同的title动画速度
            val charDelay = 45L // 统一使用标准速度

            // 🔥 【流式显示】从当前位置继续显示新字符
            for (i in displayedCharsCount until text.length) {
                displayedCharsCount = i + 1
                delay(charDelay)
            }

            isStreamingActive = false
        }
    }

    // 🔥 【内容显示】显示已流式显示的字符
    val displayedContent = text.take(displayedCharsCount)

    // 🔥 【修复：使用RainbowAnimations的rememberStreamingMetallicBrush】
    val streamingMetallicBrush = com.example.gymbro.designSystem.components.extras.rememberStreamingMetallicBrush(
        useAnimate = true,
        useHdr = false,
    )

    Text(
        text = displayedContent,
        style = androidx.compose.ui.text.TextStyle(
            fontFamily = MapleMono,  // 🔥 【字体系统修复】使用项目标准字体族
            fontSize = Tokens.Typography.Headline,
            fontStyle = if (isPerthink) FontStyle.Italic else FontStyle.Normal,
            fontWeight = FontWeight.Bold
        ).copy(
            brush = streamingMetallicBrush, // 🔥 【修复】使用流式金属动画
        ),
        modifier = modifier.let {
            if (isPerthink) {
                // 🔥 【perthink特效】添加脉冲动画
                AnimationEngine.run { it.pulseAnimation(enabled = true) }
            } else {
                it
            }
        },
        maxLines = 1,
    )
}

// ==================== Preview Functions ====================

@Composable
private fun createMockSegmentUi(
    id: String,
    kind: SegmentKind,
    title: String?,
    content: String,
    isComplete: Boolean = true,
): ThinkingBoxContract.SegmentUi {
    return ThinkingBoxContract.SegmentUi(
        id = id,
        kind = kind,
        title = title,
        content = content,
        isComplete = isComplete,
    )
}

/**
 * ThinkingStageCard Preview - 正式思考阶段
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Normal() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            segment = createMockSegmentUi(
                id = "analyze",
                kind = SegmentKind.PHASE,
                title = "分析问题",
                content = "我需要仔细分析你提出的健身计划问题，考虑你的目标、经验水平和可用时间。根据你的描述，我将从以下几个方面来制定合适的训练方案。",
            ),
            isActive = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingStageCard Preview - PreThink 阶段
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_PreThink() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            segment = createMockSegmentUi(
                id = "perthink",
                kind = SegmentKind.PERTHINK,
                title = null, // PERTHINK使用固定标题
                content = "让我想想...",
            ),
            isActive = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingStageCard Preview - 非活动状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Inactive() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            segment = createMockSegmentUi(
                id = "completed",
                kind = SegmentKind.PHASE,
                title = "制定方案",
                content = "基于分析结果，我为你制定了个性化的训练计划，包括力量训练和有氧运动的合理搭配。",
            ),
            isActive = false,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

/**
 * ThinkingStageCard Preview - 加载中状态
 */
@com.example.gymbro.designSystem.preview.GymBroPreview
@Composable
private fun ThinkingStageCardPreview_Loading() {
    com.example.gymbro.designSystem.theme.GymBroTheme {
        ThinkingStageCard(
            segment = createMockSegmentUi(
                id = "planning",
                kind = SegmentKind.PHASE,
                title = "制定训练计划",
                content = "",
                isComplete = false,
            ),
            isActive = true,
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}
