package com.example.gymbro.features.coach.aicoach.internal.effect.handlers

import com.example.gymbro.core.ai.prompt.manager.PromptModeManager
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.network.config.NetworkConfigManager

import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.coach.usecase.DetectActionableContentUseCase
import com.example.gymbro.domain.coach.usecase.HistoryContextBridgeUseCase
import com.example.gymbro.domain.coach.usecase.PrepareAiContextUseCase
import com.example.gymbro.domain.workout.usecase.GenerateStructuredPlanUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateGenerationUseCase
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * StreamEffectHandler - AI流式请求处理器 (v8.0-单一token流清理版)
 *
 * 🔥 【新架构Token系统】核心变更：
 * - 移除Coach层token处理逻辑，确保单一路径
 * - Token流完全由新架构自动处理：UnifiedTokenReceiver → StreamingProcessor → DirectOutputChannel → ThinkingBox
 * - Coach只负责AI请求启动，token处理完全交给Core-Network新架构
 * - ThinkingBox通过DirectOutputChannel直接接收处理后的token
 *
 * 核心职责：AI对话流式请求启动、Function Call检测
 */
internal class StreamEffectHandler
@Inject
constructor(
    private val aiStreamRepository: AiStreamRepository,
    private val prepareAiContextUseCase: PrepareAiContextUseCase,
    private val promptModeManager: PromptModeManager,
    private val detectActionableContentUseCase: DetectActionableContentUseCase,
    private val templateGenerationUseCase: TemplateGenerationUseCase,
    private val generateStructuredPlanUseCase: GenerateStructuredPlanUseCase,
    private val historyContextBridgeUseCase: HistoryContextBridgeUseCase,
    private val networkConfigManager: NetworkConfigManager,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {
    private lateinit var handlerScope: CoroutineScope
    private lateinit var sendIntent: (AiCoachContract.Intent) -> Unit
    private lateinit var stateProvider: () -> AiCoachContract.State

    private var currentStreamJob: Job? = null

    fun initialize(
        scope: CoroutineScope,
        intentSender: (AiCoachContract.Intent) -> Unit,
        stateProvider: () -> AiCoachContract.State,
    ) {
        this.handlerScope = scope
        this.sendIntent = intentSender
        this.stateProvider = stateProvider
    }

    fun handleStartAiStream(effect: AiCoachContract.Effect.StartAiStream) {
        currentStreamJob?.cancel()

        currentStreamJob = handlerScope.launch {
            try {
                // 准备增强的Prompt
                val enhancedPrompt = prepareEnhancedPrompt(effect)

                // 参数验证
                if (effect.sessionId.isBlank() || effect.userMessageId.isBlank() ||
                    effect.aiResponseId.isBlank() || enhancedPrompt.isBlank()
                ) {
                    sendIntent(AiCoachContract.Intent.ShowError("请求参数无效"))
                    return@launch
                }

                // 网络配置验证
                val networkConfig = networkConfigManager.getCurrentConfig()
                if (!networkConfig.validate()) {
                    sendIntent(AiCoachContract.Intent.ShowError("网络配置无效"))
                    return@launch
                }

                // 🔥 【新架构Token系统】单一路径：UnifiedTokenReceiver → StreamingProcessor → DirectOutputChannel → ThinkingBox
                Timber.tag("NEW-ARCH").i("🧹 启动新架构token流路径：messageId=${effect.aiResponseId}")

                // Step 1: 构建ChatRequest
                // 注意：ThinkingBox 将通过 DirectOutputChannel 自动接收处理后的token，无需手动调用
                val chatRequest = ChatRequest(
                    model = "deepseek-chat",
                    messages = listOf(ChatMessage(role = "user", content = enhancedPrompt)),
                    stream = true,
                    maxTokens = 4096,
                    temperature = 0.7,
                )

                Timber.tag("NEW-ARCH").d("📡 Step 2: 启动AI流式请求")

                // Step 2: 🔥 【新架构】启动AI请求并收集Flow，委托token处理给新架构
                // Coach模块职责：发送消息 → 收集Flow启动请求 → 新架构自动处理 → ThinkingBox接收和呈现
                aiStreamRepository
                    .streamChatWithMessageId(
                        request = chatRequest,
                        messageId = effect.aiResponseId,
                        taskType = com.example.gymbro.domain.coach.model.AiTaskType.CHAT,
                    ).flowOn(ioDispatcher)
                    .onEach { streamEvent ->
                        // 🔥 【新架构】Coach不处理token内容，只监听流状态
                        // token流现在完全由新架构处理：
                        // UnifiedTokenReceiver → StreamingProcessor → DirectOutputChannel → ThinkingBox

                        // 🔥 【流状态监听】只记录流事件，不处理token内容
                        Timber.tag("NEW-ARCH").d("📥 收到流事件: messageId=${effect.aiResponseId}")

                        // 流完成检查 - 根据实际的streamEvent类型进行判断
                        // 注意：具体的完成检查逻辑取决于aiStreamRepository返回的事件类型
                        // 这里保持简化，让新架构自动处理token流
                    }.catch { error ->
                        Timber.tag("NEW-ARCH").e(error, "❌ AI流异常")
                        sendIntent(AiCoachContract.Intent.ResetStreamingState)
                        sendIntent(AiCoachContract.Intent.ShowError("AI响应异常: ${error.message}"))
                    }.launchIn(this)

                Timber.tag("NEW-ARCH").i("✅ AI请求已启动，token流将通过新架构自动路由到ThinkingBox: messageId=${effect.aiResponseId}")

                Timber.tag("NEW-ARCH").i("🚀 新架构token流路径启动完成")
            } catch (e: Exception) {
                Timber.tag("NEW-ARCH").e(e, "💥 新架构token流启动失败")
                sendIntent(AiCoachContract.Intent.ResetStreamingState)
                sendIntent(AiCoachContract.Intent.ShowError("消息发送失败: ${e.message}"))
            }
        }
    }

    private suspend fun prepareEnhancedPrompt(effect: AiCoachContract.Effect.StartAiStream): String {
        // 1. 尝试使用History RAG上下文
        val historyResult = historyContextBridgeUseCase(
            sessionId = effect.sessionId,
        )

        if (historyResult is ModernResult.Success) {
            // HistoryRagContext包含summary，我们需要将其与用户prompt结合
            val historyContext = historyResult.data
            return buildPromptWithHistoryContext(effect.prompt, historyContext)
        }

        // 2. 降级到标准RAG上下文准备
        val contextResult = prepareAiContextUseCase(
            query = effect.prompt,
            sessionId = effect.sessionId,
        )

        return when (contextResult) {
            is ModernResult.Success -> {
                val context = contextResult.data

                // 触发用户资料检查
                sendIntent(AiCoachContract.Intent.CheckProfileCompletion(context.userProfile.toString()))

                // 使用PromptModeManager构建完整prompt
                buildPromptWithContext(effect.prompt, context)
            }
            is ModernResult.Error -> {
                Timber.w("RAG上下文准备失败: ${contextResult.error}")
                buildBasicPrompt(effect.prompt)
            }
            is ModernResult.Loading -> effect.prompt
        }
    }

    private suspend fun buildPromptWithHistoryContext(
        userPrompt: String,
        historyContext: com.example.gymbro.core.ai.prompt.model.HistoryRagContext,
    ): String {
        val currentPromptBuilder = promptModeManager.getCurrentPromptBuilder()
        val systemLayer = promptModeManager.getPromptRegistry().getSuite().systemLayer

        // 构建包含History RAG摘要的对话历史
        // 将摘要作为一个对话轮次添加到历史中
        val contextHistory = listOf(
            com.example.gymbro.core.ai.prompt.builder.ConversationTurn(
                user = "请基于我的历史对话背景回答问题",
                assistant = "好的，我已了解您的历史对话背景：\n${historyContext.summary}",
            ),
        )

        val chatMessages = currentPromptBuilder.buildChatMessages(
            systemLayer = systemLayer,
            userInput = userPrompt,
            history = contextHistory,
        )

        return chatMessages.joinToString("\n\n") { message ->
            when (message.role) {
                "system" -> "[SYSTEM]\n${message.content}"
                "tool" -> "[CONTEXT]\n${message.content}"
                "user" -> "[USER]\n${message.content}"
                "assistant" -> "[ASSISTANT]\n${message.content}"
                else -> "[${message.role.uppercase()}]\n${message.content}"
            }
        }
    }

    private suspend fun buildPromptWithContext(
        userPrompt: String,
        context: com.example.gymbro.shared.models.ai.RelevantAiContext,
    ): String {
        val currentPromptBuilder = promptModeManager.getCurrentPromptBuilder()
        val systemLayer = promptModeManager.getPromptRegistry().getSuite().systemLayer

        // 构建包含RAG上下文的历史记录
        val contextHistory = buildContextHistory(context)

        val chatMessages = currentPromptBuilder.buildChatMessages(
            systemLayer = systemLayer,
            userInput = userPrompt,
            history = contextHistory,
        )

        return chatMessages.joinToString("\n\n") { message ->
            when (message.role) {
                "system" -> "[SYSTEM]\n${message.content}"
                "tool" -> "[CONTEXT]\n${message.content}"
                "user" -> "[USER]\n${message.content}"
                "assistant" -> "[ASSISTANT]\n${message.content}"
                else -> "[${message.role.uppercase()}]\n${message.content}"
            }
        }
    }

    private fun buildContextHistory(context: com.example.gymbro.shared.models.ai.RelevantAiContext): List<com.example.gymbro.core.ai.prompt.builder.ConversationTurn> {
        val contextTurns = mutableListOf<com.example.gymbro.core.ai.prompt.builder.ConversationTurn>()

        // 添加用户资料和模板上下文作为一个对话轮次
        val contextInfo = buildString {
            if (context.userProfile.gender != "未知性别" || context.userProfile.age != 25) {
                append(buildProfileContext(context.userProfile))
                append("\n")
            }
            if (context.relevantTemplates.isNotEmpty()) {
                append(buildTemplatesContext(context.relevantTemplates))
            }
        }

        if (contextInfo.isNotBlank()) {
            contextTurns.add(
                com.example.gymbro.core.ai.prompt.builder.ConversationTurn(
                    user = "请了解我的基本信息和相关训练背景",
                    assistant = "好的，我已了解您的信息：\n$contextInfo",
                ),
            )
        }

        // 添加最近对话历史（转换为ConversationTurn格式）
        val recentMessages = context.recentHistory.takeLast(4) // 取最近4条消息，组成2个对话轮次
        var i = 0
        while (i < recentMessages.size - 1) {
            val currentMessage = recentMessages[i]
            val nextMessage = recentMessages[i + 1]

            if (currentMessage.sender == "user" && nextMessage.sender != "user") {
                contextTurns.add(
                    com.example.gymbro.core.ai.prompt.builder.ConversationTurn(
                        user = currentMessage.content,
                        assistant = nextMessage.content,
                    ),
                )
                i += 2
            } else {
                i++
            }
        }

        return contextTurns
    }

    private fun buildProfileContext(profile: com.example.gymbro.shared.models.ai.AiUserProfile): String {
        return buildString {
            append("用户资料：")
            append("性别：${profile.gender}，")
            append("年龄：${profile.age}岁，")
            append("身高：${profile.height}cm，")
            append("体重：${profile.weight}kg，")
            append("健身经验：${profile.experience}")
            profile.bodyFatPercentage?.let { append("，体脂率：$it%") }
        }
    }

    private fun buildTemplatesContext(templates: List<com.example.gymbro.shared.models.ai.WorkoutTemplate>): String {
        return buildString {
            append("相关训练模板：\n")
            templates.take(3).forEach { template ->
                append("- ${template.name}：${template.description}")
                if (template.targetMuscles.isNotEmpty()) {
                    append("（目标肌群：${template.targetMuscles.joinToString("、")}）")
                }
                append("\n")
            }
        }
    }

    private suspend fun buildBasicPrompt(userPrompt: String): String {
        val currentPromptBuilder = promptModeManager.getCurrentPromptBuilder()
        val systemLayer = promptModeManager.getPromptRegistry().getSuite().systemLayer

        val chatMessages = currentPromptBuilder.buildChatMessages(
            systemLayer = systemLayer,
            userInput = userPrompt,
            history = emptyList(),
        )

        return chatMessages.joinToString("\n\n") { message ->
            when (message.role) {
                "system" -> "[SYSTEM]\n${message.content}"
                "user" -> "[USER]\n${message.content}"
                else -> "[${message.role.uppercase()}]\n${message.content}"
            }
        }
    }

    fun handleDetectFunctionCall(effect: AiCoachContract.Effect.DetectFunctionCall) {
        handlerScope.launch {
            try {
                val message = com.example.gymbro.domain.coach.model.CoachMessage.AiMessage(
                    id = effect.messageId,
                    content = effect.content,
                    timestamp = System.currentTimeMillis(),
                )

                when (val result = detectActionableContentUseCase(message)) {
                    is ModernResult.Success -> {
                        val detection = result.data
                        detection.functionCallJson?.let { json ->
                            sendIntent(
                                AiCoachContract.Intent.FunctionCallDetected(effect.messageId, json),
                            )

                            when (detection.type) {
                                com.example.gymbro.domain.coach.usecase.ActionableType.TEMPLATE_GENERATION -> {
                                    sendIntent(
                                        AiCoachContract.Intent.TemplateGenerationRequested(
                                            effect.messageId,
                                            json,
                                        ),
                                    )
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.PLAN_GENERATION -> {
                                    sendIntent(
                                        AiCoachContract.Intent.PlanGenerationRequested(
                                            effect.messageId,
                                            json,
                                        ),
                                    )
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.EXERCISE_OPERATION -> {
                                    // 处理动作库操作：解析并执行动作相关的功能调用
                                    handleExerciseOperation(effect.messageId, json)
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.SESSION_OPERATION -> {
                                    // 处理训练会话操作：解析并执行会话相关的功能调用
                                    handleSessionOperation(effect.messageId, json)
                                }
                                com.example.gymbro.domain.coach.usecase.ActionableType.NONE -> {
                                    // 无可操作内容
                                }
                            }
                        }
                    }
                    is ModernResult.Error -> {
                        Timber.e("Function Call检测失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "detection",
                                    false,
                                    "检测失败: ${result.error}",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        // 检测中，无需处理
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Function Call检测异常")
                sendIntent(
                    AiCoachContract.Intent.FunctionCallProcessedResult(
                        effect.messageId,
                        AiCoachContract.FunctionCallResult("detection", false, "检测异常: ${e.message}"),
                    ),
                )
            }
        }
    }

    private fun handleExerciseOperation(messageId: String, functionCallJson: String) {
        try {
            // 解析动作操作的具体类型和参数
            // 这里可以根据functionCallJson的内容判断是查询动作、添加动作等
            val actionTriggered = when {
                functionCallJson.contains("search", ignoreCase = true) -> "动作库搜索已执行"
                functionCallJson.contains("add", ignoreCase = true) -> "新动作已添加到动作库"
                functionCallJson.contains("modify", ignoreCase = true) -> "动作信息已更新"
                else -> "动作库操作已执行"
            }

            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "exercise_operation",
                        true,
                        functionCallJson,
                        actionTriggered = actionTriggered,
                    ),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "动作库操作处理失败")
            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "exercise_operation",
                        false,
                        functionCallJson,
                        error = "动作库操作失败: ${e.message}",
                    ),
                ),
            )
        }
    }

    private fun handleSessionOperation(messageId: String, functionCallJson: String) {
        try {
            // 解析训练会话操作的具体类型和参数
            val actionTriggered = when {
                functionCallJson.contains("start", ignoreCase = true) -> "训练会话已开始"
                functionCallJson.contains("pause", ignoreCase = true) -> "训练会话已暂停"
                functionCallJson.contains("resume", ignoreCase = true) -> "训练会话已恢复"
                functionCallJson.contains("finish", ignoreCase = true) -> "训练会话已完成"
                functionCallJson.contains("record", ignoreCase = true) -> "训练数据已记录"
                else -> "训练会话操作已执行"
            }

            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "session_operation",
                        true,
                        functionCallJson,
                        actionTriggered = actionTriggered,
                    ),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "训练会话操作处理失败")
            sendIntent(
                AiCoachContract.Intent.FunctionCallProcessedResult(
                    messageId,
                    AiCoachContract.FunctionCallResult(
                        "session_operation",
                        false,
                        functionCallJson,
                        error = "训练会话操作失败: ${e.message}",
                    ),
                ),
            )
        }
    }

    fun handleProcessTemplateGeneration(effect: AiCoachContract.Effect.ProcessTemplateGeneration) {
        handlerScope.launch {
            try {
                val generateFromResponseUseCase = templateGenerationUseCase.GenerateFromResponse()
                val params = TemplateGenerationUseCase.GenerateFromResponseParams(effect.functionCallJson)

                when (val result = generateFromResponseUseCase(params)) {
                    is ModernResult.Success -> {
                        val template = result.data
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "template_generation",
                                    true,
                                    effect.functionCallJson,
                                    actionTriggered = "训练模板「${template.name}」生成成功",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        Timber.e("训练模板生成失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "template_generation",
                                    false,
                                    effect.functionCallJson,
                                    error = "模板生成失败: ${result.error}",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        // 生成中，无需处理
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "训练模板生成处理异常")
                sendIntent(
                    AiCoachContract.Intent.FunctionCallProcessedResult(
                        effect.messageId,
                        AiCoachContract.FunctionCallResult(
                            "template_generation",
                            false,
                            effect.functionCallJson,
                            error = "模板生成失败: ${e.message}",
                        ),
                    ),
                )
            }
        }
    }

    fun handleProcessPlanGeneration(effect: AiCoachContract.Effect.ProcessPlanGeneration) {
        handlerScope.launch {
            try {
                when (val result = generateStructuredPlanUseCase(effect.functionCallJson)) {
                    is ModernResult.Success -> {
                        when (val planResult = result.data) {
                            is com.example.gymbro.domain.workout.usecase.GeneratePlanResult.Success -> {
                                sendIntent(
                                    AiCoachContract.Intent.FunctionCallProcessedResult(
                                        effect.messageId,
                                        AiCoachContract.FunctionCallResult(
                                            "plan_generation",
                                            true,
                                            effect.functionCallJson,
                                            actionTriggered = "训练计划「${planResult.planName}」生成成功，时长${planResult.duration}",
                                        ),
                                    ),
                                )
                            }
                            is com.example.gymbro.domain.workout.usecase.GeneratePlanResult.Partial -> {
                                Timber.w("训练计划部分生成: planId=${planResult.planId}")
                                sendIntent(
                                    AiCoachContract.Intent.FunctionCallProcessedResult(
                                        effect.messageId,
                                        AiCoachContract.FunctionCallResult(
                                            "plan_generation",
                                            true,
                                            effect.functionCallJson,
                                            actionTriggered = "训练计划生成成功，但有${planResult.issues.size}个问题需要注意",
                                        ),
                                    ),
                                )
                            }
                        }
                    }
                    is ModernResult.Error -> {
                        Timber.e("训练计划生成失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.FunctionCallProcessedResult(
                                effect.messageId,
                                AiCoachContract.FunctionCallResult(
                                    "plan_generation",
                                    false,
                                    effect.functionCallJson,
                                    error = "计划生成失败: ${result.error}",
                                ),
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        // 生成中，无需处理
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "训练计划生成处理异常")
                sendIntent(
                    AiCoachContract.Intent.FunctionCallProcessedResult(
                        effect.messageId,
                        AiCoachContract.FunctionCallResult(
                            "plan_generation",
                            false,
                            effect.functionCallJson,
                            error = "计划生成失败: ${e.message}",
                        ),
                    ),
                )
            }
        }
    }
}
