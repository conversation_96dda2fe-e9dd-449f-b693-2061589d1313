package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import app.cash.turbine.test
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * ThinkingBoxViewModel单元测试
 *
 * 🎯 测试目标：验证MVI架构的ViewModel层状态管理
 * 📊 覆盖率目标：≥75%
 * 🔧 测试框架：JUnit 5 + kotlin.test + Turbine
 * 🔥 重点：P0修复后的状态转换和Effect处理验证
 */
@OptIn(ExperimentalCoroutinesApi::class)
@DisplayName("ThinkingBoxViewModel")
class ThinkingBoxViewModelTest {

    private lateinit var mockDirectOutputChannel: DirectOutputChannel
    private lateinit var mockStreamingParser: StreamingThinkingMLParser
    private lateinit var mockDomainMapper: DomainMapper
    private lateinit var mockReducer: SegmentQueueReducer
    private lateinit var viewModel: ThinkingBoxViewModel
    private val testScope = TestScope()

    @BeforeEach
    fun setUp() {
        mockDirectOutputChannel = mockk()
        mockStreamingParser = mockk()
        mockDomainMapper = mockk()
        mockReducer = mockk()

        // Mock reducer的默认行为
        every { mockReducer.reduce(any(), any()) } returns SegmentQueueReducer.ReduceResult(
            state = SegmentQueueReducer.TBState(messageId = "test"),
            effects = emptyList()
        )

        viewModel = ThinkingBoxViewModel(
            directOutputChannel = mockDirectOutputChannel,
            streamingParser = mockStreamingParser,
            domainMapper = mockDomainMapper,
            segmentQueueReducer = mockReducer
        )
    }

    @Nested
    @DisplayName("初始化测试")
    inner class InitializationTests {

        @Test
        @DisplayName("ViewModel应该有正确的初始状态")
        fun `ViewModel should have correct initial state`() = testScope.runTest {
            // When
            viewModel.state.test {
                val initialState = awaitItem()

                // Then - 只验证最基本的状态
                assertEquals("", initialState.messageId)
                assertTrue(initialState.segmentsQueue.isEmpty())
                assertEquals("", initialState.finalContent)
            }
        }

        @Test
        @DisplayName("initialize应该设置messageId")
        fun `initialize should set messageId`() = testScope.runTest {
            // Given
            val testMessageId = "test-message-123"

            // When
            viewModel.initialize(testMessageId)

            // Then
            viewModel.state.test {
                val state = awaitItem()
                // 验证初始化后的状态 - initialize方法实际上设置了messageId
                assertEquals(testMessageId, state.messageId)
            }
        }
    }

    @Nested
    @DisplayName("初始化和状态测试")
    inner class InitializationAndStateTests {

        @Test
        @DisplayName("initialize应该正确初始化ViewModel")
        fun `initialize should correctly initialize ViewModel`() = testScope.runTest {
            // Given
            val testMessageId = "test-message-123"

            // When
            viewModel.initialize(testMessageId)

            // Then
            viewModel.state.test {
                val state = awaitItem()
                // 验证初始状态
                assertTrue(state.segmentsQueue.isEmpty())
                assertFalse(state.finalReady)
                assertEquals("", state.finalContent)
                // 注意：streaming属性已被删除，现在是Flow-derived
                assertFalse(state.thinkingClosed)
            }
        }

        @Test
        @DisplayName("多次初始化同一messageId应该跳过重复初始化")
        fun `multiple initialize calls with same messageId should skip duplicate initialization`() = testScope.runTest {
            // Given
            val testMessageId = "test-message-123"

            // When
            viewModel.initialize(testMessageId)
            viewModel.initialize(testMessageId) // 第二次调用应该被跳过

            // Then
            viewModel.state.test {
                val state = awaitItem()
                // 验证状态保持一致
                assertTrue(state.segmentsQueue.isEmpty())
                assertFalse(state.finalReady)
            }
        }
    }

    @Nested
    @DisplayName("状态流测试")
    inner class StateFlowTests {

        @Test
        @DisplayName("state StateFlow应该正确暴露状态")
        fun `state StateFlow should correctly expose state`() = testScope.runTest {
            // When
            viewModel.state.test {
                val initialState = awaitItem()

                // Then
                assertNotNull(initialState)
                assertTrue(initialState.segmentsQueue.isEmpty())
                assertEquals("", initialState.messageId)
                assertFalse(initialState.finalReady)
                assertEquals("", initialState.finalContent)
                // 注意：streaming属性已被删除，现在是Flow-derived
                assertFalse(initialState.thinkingClosed)
                assertNull(initialState.currentSegment)
            }
        }

        @Test
        @DisplayName("effect StateFlow应该正确暴露Effect")
        fun `effect StateFlow should correctly expose effects`() = testScope.runTest {
            // When
            viewModel.effect.test {
                // StateFlow会发出初始值null
                assertEquals(null, awaitItem()) // 初始状态下Effect为null
            }
        }
    }
}
