package com.example.gymbro.core.network.client

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.network.config.NetworkConfig
import com.example.gymbro.shared.models.network.StreamRequest
import com.example.gymbro.shared.models.network.StreamResponse
import io.mockk.*
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import okhttp3.OkHttpClient
import okhttp3.mockwebserver.MockWebServer
import okhttp3.mockwebserver.MockResponse
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.*

/**
 * AdaptiveStreamClient 测试套件
 * 
 * 🎯 测试覆盖范围：
 * - SSE流式连接和数据接收
 * - 错误处理和重试机制
 * - 连接状态管理
 * - 性能和超时处理
 * - 网络异常恢复
 */
class AdaptiveStreamClientTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var adaptiveStreamClient: AdaptiveStreamClient
    private lateinit var networkConfig: NetworkConfig
    private lateinit var okHttpClient: OkHttpClient

    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()

        networkConfig = NetworkConfig(
            wsBase = mockWebServer.url("/").toString(),
            restBase = mockWebServer.url("/").toString(),
            apiKey = "test-api-key",
            connectTimeoutSec = 10,
            readTimeoutSec = 30,
            writeTimeoutSec = 30,
            enableRetry = true,
            maxRetries = 3,
            retryDelayMs = 1000
        )

        okHttpClient = OkHttpClient.Builder()
            .connectTimeout(networkConfig.connectTimeoutSec.toLong(), java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(networkConfig.readTimeoutSec.toLong(), java.util.concurrent.TimeUnit.SECONDS)
            .writeTimeout(networkConfig.writeTimeoutSec.toLong(), java.util.concurrent.TimeUnit.SECONDS)
            .build()

        // 注意：这里需要根据实际的AdaptiveStreamClient构造函数调整
        adaptiveStreamClient = mockk(relaxed = true) // 临时mock，需要根据实际情况调整
    }

    @After
    fun tearDown() {
        mockWebServer.shutdown()
    }

    // ==================== 基础流式连接测试 ====================

    @Test
    fun `given valid request, when stream called, then SSE connection established`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        // Mock successful SSE response
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setHeader("Cache-Control", "no-cache")
                .setBody("""
                    data: {"content": "Hello"}
                    
                    data: {"content": " World"}
                    
                    data: [DONE]
                    
                """.trimIndent())
        )

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        assertTrue(result is ModernResult.Success)
        // 需要根据实际的返回类型验证流数据
    }

    @Test
    fun `given network error, when stream called, then error result returned`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        // Mock network error
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(500)
                .setBody("Internal Server Error")
        )

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        assertTrue(result is ModernResult.Failure)
    }

    @Test
    fun `given timeout, when stream called, then timeout error returned`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        // Mock slow response that will timeout
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBodyDelay(35, java.util.concurrent.TimeUnit.SECONDS) // 超过readTimeout
                .setBody("data: slow response")
        )

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        assertTrue(result is ModernResult.Failure)
        // 验证错误类型是超时错误
    }

    // ==================== 重试机制测试 ====================

    @Test
    fun `given temporary network failure, when stream called with retry enabled, then request retried`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        // Mock first request fails, second succeeds
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(503) // Service Unavailable
                .setBody("Service temporarily unavailable")
        )
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBody("data: {\"content\": \"Success after retry\"}")
        )

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        // 验证重试机制工作正常
        assertEquals(2, mockWebServer.requestCount)
        assertTrue(result is ModernResult.Success)
    }

    @Test
    fun `given max retries exceeded, when stream called, then failure result returned`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        // Mock all requests fail
        repeat(networkConfig.maxRetries + 1) {
            mockWebServer.enqueue(
                MockResponse()
                    .setResponseCode(503)
                    .setBody("Service unavailable")
            )
        }

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        assertTrue(result is ModernResult.Failure)
        assertEquals(networkConfig.maxRetries + 1, mockWebServer.requestCount)
    }

    // ==================== 数据流处理测试 ====================

    @Test
    fun `given chunked SSE data, when stream received, then all chunks processed correctly`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        val sseData = """
            data: {"content": "Chunk 1"}
            
            data: {"content": "Chunk 2"}
            
            data: {"content": "Chunk 3"}
            
            data: [DONE]
            
        """.trimIndent()

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBody(sseData)
        )

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        assertTrue(result is ModernResult.Success)
        // 验证所有数据块都被正确处理
    }

    @Test
    fun `given malformed SSE data, when stream received, then error handled gracefully`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        val malformedSseData = """
            data: {"content": "Valid chunk"}
            
            invalid_line_without_data_prefix
            
            data: {"content": "Another valid chunk"}
            
        """.trimIndent()

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBody(malformedSseData)
        )

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        // 验证错误被优雅处理，有效数据仍然被处理
        assertTrue(result is ModernResult.Success || result is ModernResult.Failure)
    }

    // ==================== 认证和头部测试 ====================

    @Test
    fun `given API key, when stream called, then authorization header included`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBody("data: {\"content\": \"Authenticated response\"}")
        )

        // When
        adaptiveStreamClient.stream(request)

        // Then
        val recordedRequest = mockWebServer.takeRequest()
        assertNotNull(recordedRequest.getHeader("Authorization"))
        assertTrue(recordedRequest.getHeader("Authorization")!!.contains(networkConfig.apiKey))
    }

    @Test
    fun `given custom headers, when stream called, then headers included in request`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBody("data: {\"content\": \"Response with custom headers\"}")
        )

        // When
        adaptiveStreamClient.stream(request)

        // Then
        val recordedRequest = mockWebServer.takeRequest()
        assertEquals("text/event-stream", recordedRequest.getHeader("Accept"))
        assertEquals("no-cache", recordedRequest.getHeader("Cache-Control"))
    }

    // ==================== 性能测试 ====================

    @Test
    fun `given large SSE stream, when data received, then performance acceptable`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        // 生成大量SSE数据
        val largeData = StringBuilder()
        repeat(1000) { index ->
            largeData.append("data: {\"content\": \"Chunk $index\"}\n\n")
        }
        largeData.append("data: [DONE]\n\n")

        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBody(largeData.toString())
        )

        // When
        val startTime = System.currentTimeMillis()
        val result = adaptiveStreamClient.stream(request)
        val endTime = System.currentTimeMillis()

        // Then
        val processingTime = endTime - startTime
        assertTrue(processingTime < 5000, "Large stream processing took too long: ${processingTime}ms")
        assertTrue(result is ModernResult.Success)
    }

    // ==================== 连接状态测试 ====================

    @Test
    fun `given connection interrupted, when stream in progress, then error reported`() = runTest {
        // Given
        val request = StreamRequest(
            messages = listOf(),
            model = "test-model",
            stream = true
        )

        // Mock connection that gets interrupted
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "text/event-stream")
                .setBody("data: {\"content\": \"Partial data\"}")
                .setSocketPolicy(okhttp3.mockwebserver.SocketPolicy.DISCONNECT_DURING_RESPONSE_BODY)
        )

        // When
        val result = adaptiveStreamClient.stream(request)

        // Then
        assertTrue(result is ModernResult.Failure)
    }
}
