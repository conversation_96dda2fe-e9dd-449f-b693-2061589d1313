package com.example.gymbro.core.network.debug

import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.network.receiver.UnifiedTokenReceiver
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.data.coach.service.AiResponseReceiver
import com.example.gymbro.data.coach.repository.AiStreamRepositoryImpl
import com.example.gymbro.shared.models.ai.ChatRequest
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.domain.coach.model.AiTaskType
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import javax.inject.Inject

/**
 * Token流传递链验证测试
 * 
 * 验证从Coach发起AI请求到ThinkingBox接收token的完整流程
 */
class TokenFlowValidationTest @Inject constructor(
    private val aiStreamRepository: AiStreamRepositoryImpl,
    private val aiResponseReceiver: AiResponseReceiver,
    private val unifiedTokenReceiver: UnifiedTokenReceiver,
    private val directOutputChannel: DirectOutputChannel
) {
    
    companion object {
        private const val TAG = "TokenFlowValidation"
        private const val TEST_MESSAGE_ID = "test-message-12345"
        private const val TEST_TIMEOUT_MS = 30000L // 30秒超时
    }
    
    /**
     * 验证完整的token流传递链
     */
    suspend fun validateCompleteTokenFlow(): ValidationResult {
        Timber.tag(TAG).i("🧪 开始验证Token流传递链")
        
        return try {
            // 1. 模拟Coach发起AI请求
            val chatRequest = createTestChatRequest()
            Timber.tag(TAG).d("📝 创建测试ChatRequest: ${chatRequest.messages.size}条消息")
            
            // 2. 设置ThinkingBox监听器（模拟ThinkingBox订阅）
            val receivedTokens = mutableListOf<String>()
            val thinkingBoxJob = startThinkingBoxListener(receivedTokens)
            
            // 3. 启动AI请求流
            val requestJob = startAiRequestFlow(chatRequest, receivedTokens)
            
            // 4. 等待结果或超时
            val result = withTimeoutOrNull(TEST_TIMEOUT_MS) {
                // 等待至少收到一些token
                while (receivedTokens.isEmpty()) {
                    delay(100)
                }
                
                // 等待流程完成
                requestJob.join()
                thinkingBoxJob.cancel()
                
                ValidationResult.Success(
                    tokensReceived = receivedTokens.size,
                    firstToken = receivedTokens.firstOrNull() ?: "",
                    lastToken = receivedTokens.lastOrNull() ?: "",
                    totalDuration = System.currentTimeMillis()
                )
            }
            
            result ?: ValidationResult.Timeout("验证超时，未在${TEST_TIMEOUT_MS}ms内完成")
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ Token流验证失败")
            ValidationResult.Error(e.message ?: "未知错误", e)
        }
    }
    
    /**
     * 创建测试用的ChatRequest
     */
    private fun createTestChatRequest(): ChatRequest {
        return ChatRequest(
            model = "deepseek-chat",
            messages = listOf(
                ChatMessage(
                    role = "user",
                    content = "请简单介绍一下健身的好处，用thinking标签思考。"
                )
            ),
            stream = true,
            maxTokens = 1000,
            temperature = 0.7
        )
    }
    
    /**
     * 启动ThinkingBox监听器（模拟ThinkingBox订阅DirectOutputChannel）
     */
    private fun CoroutineScope.startThinkingBoxListener(
        receivedTokens: MutableList<String>
    ): Job {
        return launch {
            try {
                Timber.tag(TAG).d("👂 启动ThinkingBox监听器")
                
                directOutputChannel.subscribeToConversation(TEST_MESSAGE_ID)
                    .collect { outputToken ->
                        val token = outputToken.content
                        receivedTokens.add(token)
                        
                        Timber.tag(TAG).v("📥 ThinkingBox收到token: '${token.take(30)}${if (token.length > 30) "..." else ""}' (${receivedTokens.size})")
                        
                        // 检查是否完成
                        if (outputToken.metadata["isComplete"] == true) {
                            Timber.tag(TAG).i("✅ Token流完成，共收到${receivedTokens.size}个token")
                            return@collect
                        }
                    }
                    
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ ThinkingBox监听器错误")
            }
        }
    }
    
    /**
     * 启动AI请求流（模拟Coach发起请求）
     */
    private fun CoroutineScope.startAiRequestFlow(
        chatRequest: ChatRequest,
        receivedTokens: MutableList<String>
    ): Job {
        return launch {
            try {
                Timber.tag(TAG).d("🚀 启动AI请求流")
                
                // 模拟Coach调用AiStreamRepository
                aiStreamRepository.streamChatWithMessageId(
                    request = chatRequest,
                    messageId = TEST_MESSAGE_ID,
                    taskType = AiTaskType.CHAT
                ).collect { outputToken ->
                    // Coach收到的token（用于验证双重订阅）
                    Timber.tag(TAG).v("📤 Coach收到token: '${outputToken.content.take(20)}...'")
                }
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ AI请求流错误")
            }
        }
    }
    
    /**
     * 验证DirectOutputChannel的基本功能
     */
    suspend fun validateDirectOutputChannel(): ValidationResult {
        Timber.tag(TAG).i("🧪 验证DirectOutputChannel基本功能")
        
        return try {
            val testTokens = listOf("Hello", " ", "World", "!")
            val receivedTokens = mutableListOf<String>()
            
            // 启动监听
            val listenerJob = CoroutineScope(Dispatchers.IO).launch {
                directOutputChannel.subscribeToConversation(TEST_MESSAGE_ID)
                    .collect { outputToken ->
                        receivedTokens.add(outputToken.content)
                    }
            }
            
            // 发送测试token
            testTokens.forEach { token ->
                directOutputChannel.sendToken(
                    token = token,
                    conversationId = TEST_MESSAGE_ID,
                    contentType = ContentType.PLAIN_TEXT
                )
                delay(10) // 小延迟确保顺序
            }
            
            // 等待接收
            delay(100)
            listenerJob.cancel()
            
            if (receivedTokens == testTokens) {
                ValidationResult.Success(
                    tokensReceived = receivedTokens.size,
                    firstToken = receivedTokens.firstOrNull() ?: "",
                    lastToken = receivedTokens.lastOrNull() ?: "",
                    totalDuration = System.currentTimeMillis()
                )
            } else {
                ValidationResult.Error(
                    "Token顺序不匹配: 期望$testTokens, 实际$receivedTokens",
                    null
                )
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ DirectOutputChannel验证失败")
            ValidationResult.Error(e.message ?: "未知错误", e)
        }
    }
    
    /**
     * 验证结果
     */
    sealed class ValidationResult {
        data class Success(
            val tokensReceived: Int,
            val firstToken: String,
            val lastToken: String,
            val totalDuration: Long
        ) : ValidationResult()
        
        data class Error(
            val message: String,
            val exception: Throwable?
        ) : ValidationResult()
        
        data class Timeout(
            val message: String
        ) : ValidationResult()
    }
    
    /**
     * 运行所有验证测试
     */
    suspend fun runAllValidations(): Map<String, ValidationResult> {
        val results = mutableMapOf<String, ValidationResult>()
        
        Timber.tag(TAG).i("🧪 开始运行所有Token流验证测试")
        
        // 1. 验证DirectOutputChannel基本功能
        results["DirectOutputChannel"] = validateDirectOutputChannel()
        
        // 2. 验证完整Token流传递链
        results["CompleteTokenFlow"] = validateCompleteTokenFlow()
        
        // 3. 输出结果摘要
        results.forEach { (testName, result) ->
            when (result) {
                is ValidationResult.Success -> {
                    Timber.tag(TAG).i("✅ $testName: 成功 (${result.tokensReceived}个token)")
                }
                is ValidationResult.Error -> {
                    Timber.tag(TAG).e("❌ $testName: 失败 - ${result.message}")
                }
                is ValidationResult.Timeout -> {
                    Timber.tag(TAG).w("⏰ $testName: 超时 - ${result.message}")
                }
            }
        }
        
        return results
    }
}
