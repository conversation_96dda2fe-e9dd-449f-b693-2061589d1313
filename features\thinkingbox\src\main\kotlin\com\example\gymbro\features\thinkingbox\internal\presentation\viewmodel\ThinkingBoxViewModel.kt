package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ThinkingBoxViewModel - Segment队列架构 ViewModel（729方案4.md）
 *
 * 🎯 核心职责：
 * - 直接使用 SegmentQueueReducer 管理状态
 * - 协调 Token 流解析和状态更新
 * - 管理 Effect 分发和处理
 * - 支持完整的生命周期管理
 *
 * 🔥 架构特点：
 * - 移除 MVI 2.0 包装，直接使用 SegmentQueueReducer
 * - 简化状态管理，专注于 Segment 队列逻辑
 * - 集成 DomainMapper 和 StreamingParser
 * - 支持 History 写入通过 Effect 分发
 */
@HiltViewModel
class ThinkingBoxViewModel @Inject constructor(
    private val segmentQueueReducer: SegmentQueueReducer,
    private val domainMapper: DomainMapper,
    private val streamingParser: StreamingThinkingMLParser,
    private val directOutputChannel: DirectOutputChannel,
) : ViewModel() {

    // 内部状态
    private var internalState = SegmentQueueReducer.TBState()
    private var mappingContext = DomainMapper.MappingContext()

    // 公开状态
    private val _state = MutableStateFlow(ThinkingBoxContract.State())
    val state: StateFlow<ThinkingBoxContract.State> = _state.asStateFlow()

    // Effect 流
    private val _effect = MutableStateFlow<ThinkingBoxContract.Effect?>(null)
    val effect: StateFlow<ThinkingBoxContract.Effect?> = _effect.asStateFlow()

    // 解析任务
    private var parseJob: Job? = null

    init {
        Timber.tag("TB-VIEWMODEL").d("🚀 ThinkingBoxViewModel 初始化")
    }

    /**
     * 初始化 ThinkingBox
     *
     * @param messageId 消息ID
     */
    fun initialize(messageId: String) {
        Timber.tag("TB-VIEWMODEL").i("🚀 初始化ThinkingBox: messageId=$messageId")

        // 防止重复初始化
        if (internalState.messageId == messageId) {
            Timber.tag("TB-VIEWMODEL").d("跳过重复初始化: $messageId")
            return
        }

        // 重置状态
        internalState = SegmentQueueReducer.TBState(messageId = messageId)
        mappingContext = DomainMapper.MappingContext()

        // 更新 Contract State
        updateContractState()

        // 启动 Token 流监听
        startTokenStreamListening(messageId)
    }

    /**
     * 处理 UI 段渲染完成
     */
    fun onSegmentRendered(segmentId: String) {
        Timber.tag("TB-VIEWMODEL").d("📤 段渲染完成: $segmentId")

        val event = com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent.UiSegmentRendered(segmentId)
        processThinkingEvent(event)
    }

    /**
     * 重置状态
     */
    fun reset() {
        Timber.tag("TB-VIEWMODEL").i("🔄 重置状态")

        // 取消解析任务
        parseJob?.cancel()
        parseJob = null

        // 重置状态
        internalState = SegmentQueueReducer.TBState()
        mappingContext = DomainMapper.MappingContext()

        // 更新 Contract State
        updateContractState()
    }

    /**
     * 启动 Token 流监听
     */
    private fun startTokenStreamListening(messageId: String) {
        Timber.tag("TB-VIEWMODEL").i("🎯 [Token流启动] messageId=$messageId")

        // 取消之前的解析任务
        parseJob?.cancel()

        parseJob = viewModelScope.launch {
            try {
                Timber.tag("TB-VIEWMODEL").i("✅ [开始处理消息] messageId=$messageId")

                // 🔥 【新架构】使用DirectOutputChannel订阅token流
                // HTTP → UnifiedTokenReceiver → StreamingProcessor → DirectOutputChannel → ThinkingBox
                directOutputChannel.subscribeToConversation(messageId)
                    .collect { outputToken ->
                        val token = outputToken.content
                        Timber.tag("TB-VIEWMODEL").d("📥 [新架构Token] messageId=$messageId, token='${token.take(50)}${if (token.length > 50) "..." else ""}', type=${outputToken.contentType}")

                        // 使用StreamingParser解析token
                        // 注意：这里需要将单个token转换为Flow
                        streamingParser.parseTokenStream(
                            messageId = messageId,
                            tokens = kotlinx.coroutines.flow.flowOf(token),
                            onEvent = { semanticEvent ->
                                // 将语义事件映射为思考事件
                                val mappingResult = domainMapper.mapSemanticToThinking(semanticEvent, mappingContext)

                                // 处理映射结果
                                mappingResult.events.forEach { thinkingEvent ->
                                    processThinkingEvent(thinkingEvent)
                                }

                                // 更新映射上下文
                                mappingContext = mappingResult.context
                            }
                        )
                    }

            } catch (e: Exception) {
                Timber.tag("TB-VIEWMODEL").e(e, "❌ [Token流启动失败] messageId=$messageId")
                emitEffect(ThinkingBoxContract.Effect.ShowError(
                    com.example.gymbro.core.ui.text.UiText.DynamicString("Token流启动失败: ${e.message}")
                ))
            }
        }
    }

    /**
     * 处理 ThinkingEvent
     */
    private fun processThinkingEvent(event: com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent) {
        // 使用 SegmentQueueReducer 处理事件
        val reduceResult = segmentQueueReducer.reduce(internalState, event)

        // 更新内部状态
        internalState = reduceResult.state

        // 更新 Contract State
        updateContractState()

        // 分发 Effects
        reduceResult.effects.forEach { effect ->
            emitEffect(effect)
        }
    }

    /**
     * 将内部 TBState 转换为 Contract State
     */
    private fun updateContractState() {
        val contractState = convertToContractState(internalState)
        _state.value = contractState
    }

    /**
     * 转换为 Contract State
     */
    private fun convertToContractState(tbState: SegmentQueueReducer.TBState): ThinkingBoxContract.State {
        // 获取待渲染的段队列
        val queueSegments = buildList {
            // 添加当前段（如果已闭合）
            tbState.current?.let { current ->
                if (current.closed && current.getTextContent().isNotEmpty()) {
                    add(convertToSegmentUi(current))
                }
            }
            // 添加队列中的段
            tbState.queue.forEach { segment ->
                add(convertToSegmentUi(segment))
            }
        }

        return ThinkingBoxContract.State(
            messageId = tbState.messageId ?: "",
            segmentsQueue = queueSegments,
            currentSegment = tbState.current?.let { convertToSegmentUi(it) },
            finalReady = tbState.isFinalReadyToRender(),
            finalContent = tbState.getFinalContent(),
            thinkingClosed = tbState.thinkingClosed,
            isLoading = false
        )
    }

    /**
     * 将 Domain Segment 转换为 Contract SegmentUi
     */
    private fun convertToSegmentUi(segment: com.example.gymbro.features.thinkingbox.domain.model.Segment): ThinkingBoxContract.SegmentUi {
        return ThinkingBoxContract.SegmentUi(
            id = segment.id,
            kind = segment.kind,
            title = segment.title,
            content = segment.getTextContent(),
            isComplete = segment.closed
        )
    }

    /**
     * 发送 Effect
     */
    private fun emitEffect(effect: ThinkingBoxContract.Effect) {
        _effect.value = effect
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        parseJob?.cancel()
        Timber.tag("TB-VIEWMODEL").d("🧹 ThinkingBoxViewModel 清理完成")
    }
}
