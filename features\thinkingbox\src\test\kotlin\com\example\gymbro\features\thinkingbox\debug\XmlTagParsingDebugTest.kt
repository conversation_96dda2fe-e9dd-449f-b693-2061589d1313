package com.example.gymbro.features.thinkingbox.debug

import com.example.gymbro.core.network.security.StringXmlEscaper
import com.example.gymbro.features.thinkingbox.domain.parser.XmlStreamScanner
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * XML标签解析调试测试
 *
 * 🎯 测试目标：验证thinking标签解析问题的修复
 * 📊 覆盖场景：
 * 1. StringXmlEscaper的XML标签保护逻辑
 * 2. XmlStreamScanner的跨token标签解析
 * 3. 完整的thinking标签处理流程
 */
@DisplayName("XML标签解析调试测试")
class XmlTagParsingDebugTest {

    private lateinit var stringXmlEscaper: StringXmlEscaper
    private lateinit var xmlStreamScanner: XmlStreamScanner

    @BeforeEach
    fun setup() {
        stringXmlEscaper = StringXmlEscaper()
        xmlStreamScanner = XmlStreamScanner()
    }

    @Test
    @DisplayName("StringXmlEscaper应该保护单字符XML标签")
    fun `stringXmlEscaper should protect single character xml tags`() {
        // 测试单个XML标签字符的保护
        val leftBracket = stringXmlEscaper.sanitizeToken("<")
        val rightBracket = stringXmlEscaper.sanitizeToken(">")

        assertEquals("<", leftBracket, "单个'<'字符应该被保护")
        assertEquals(">", rightBracket, "单个'>'字符应该被保护")
    }

    @Test
    @DisplayName("StringXmlEscaper应该保护短XML标签片段")
    fun `stringXmlEscaper should protect short xml tag fragments`() {
        // 测试短XML标签片段的保护
        val fragments = listOf("<t", "th", "ink", "ing", ">")

        fragments.forEach { fragment ->
            val result = stringXmlEscaper.sanitizeToken(fragment)
            assertEquals(fragment, result, "XML标签片段'$fragment'应该被保护")
        }
    }

    @Test
    @DisplayName("XmlStreamScanner应该能够处理跨token的thinking标签")
    fun `xmlStreamScanner should handle thinking tag across tokens`() {
        // 模拟thinking标签被分割成多个token的情况
        val tokens = listOf("<", "t", "h", "i", "n", "k", "i", "n", "g", ">")
        val allTokens = mutableListOf<XmlStreamScanner.Token>()

        // 逐个feed token
        tokens.forEach { token ->
            val parsedTokens = xmlStreamScanner.feed(token)
            allTokens.addAll(parsedTokens)
        }

        // 验证最终能够解析出完整的thinking标签
        val tagOpenTokens = allTokens.filterIsInstance<XmlStreamScanner.TagOpen>()
        assertTrue(tagOpenTokens.isNotEmpty(), "应该解析出TagOpen token")

        val thinkingTag = tagOpenTokens.find { it.name == "thinking" }
        assertTrue(thinkingTag != null, "应该解析出thinking标签")
    }

    @Test
    @DisplayName("完整的thinking标签处理流程测试")
    fun `complete thinking tag processing flow test`() {
        // 模拟完整的thinking标签处理流程
        val thinkingContent = "<thinking>这是思考内容</thinking>"

        // 1. 分割成单字符token（模拟AI输出）
        val tokens = thinkingContent.toCharArray().map { it.toString() }

        // 2. 通过StringXmlEscaper处理每个token
        val processedTokens = tokens.map { token ->
            stringXmlEscaper.sanitizeToken(token)
        }

        // 3. 重新组合
        val reassembledContent = processedTokens.joinToString("")

        // 4. 通过XmlStreamScanner解析
        xmlStreamScanner.clear() // 清理缓冲区
        val parsedTokens = xmlStreamScanner.feed(reassembledContent)

        // 验证结果
        assertEquals(thinkingContent, reassembledContent, "重新组合的内容应该与原始内容一致")

        val tagOpenTokens = parsedTokens.filterIsInstance<XmlStreamScanner.TagOpen>()
        val tagCloseTokens = parsedTokens.filterIsInstance<XmlStreamScanner.TagClose>()
        val textTokens = parsedTokens.filterIsInstance<XmlStreamScanner.Text>()

        assertTrue(tagOpenTokens.any { it.name == "thinking" }, "应该解析出thinking开标签")
        assertTrue(tagCloseTokens.any { it.name == "thinking" }, "应该解析出thinking闭标签")
        assertTrue(textTokens.any { it.content.contains("思考内容") }, "应该解析出文本内容")
    }

    @Test
    @DisplayName("验证thinking标签会被正确转义")
    fun `verify thinking tag is correctly escaped`() {
        // 测试thinking标签在各种情况下都会被正确转义
        val testCases = listOf(
            "<thinking>",
            "</thinking>",
            "<thinking>内容</thinking>",
            "前文<thinking>内容</thinking>后文"
        )

        testCases.forEach { testCase ->
            val result = stringXmlEscaper.sanitizeAndEscape(testCase)
            assertTrue(result.contains("&lt;thinking&gt;") || result.contains("&lt;/thinking&gt;"),
                "thinking标签在内容'$testCase'中应该被转义，实际结果：$result")
        }
    }

    @Test
    @DisplayName("验证XML标签保护逻辑的边界情况")
    fun `verify xml tag protection edge cases`() {
        // 测试各种边界情况
        val edgeCases = mapOf(
            "<" to "<",           // 单个左括号
            ">" to ">",           // 单个右括号
            "<>" to "<>",         // 空标签
            "<t" to "<t",         // 不完整标签开始
            "ng>" to "ng>",       // 不完整标签结束
            "<<" to "<<",         // 双左括号
            ">>" to ">>",         // 双右括号
        )

        edgeCases.forEach { (input, expected) ->
            val result = stringXmlEscaper.sanitizeToken(input)
            assertEquals(expected, result, "边界情况'$input'的处理结果不正确")
        }
    }

    @Test
    @DisplayName("性能测试：大量token处理")
    fun `performance test for large number of tokens`() {
        // 生成大量的单字符token
        val largeContent = "<thinking>" + "内容".repeat(1000) + "</thinking>"
        val tokens = largeContent.toCharArray().map { it.toString() }

        // 测试处理时间
        val startTime = System.currentTimeMillis()

        val processedTokens = tokens.map { token ->
            stringXmlEscaper.sanitizeToken(token)
        }

        val endTime = System.currentTimeMillis()
        val processingTime = endTime - startTime

        // 验证结果正确性
        val reassembledContent = processedTokens.joinToString("")
        assertEquals(largeContent, reassembledContent, "大量token处理后内容应该保持一致")

        // 验证性能（应该在合理时间内完成）
        assertTrue(processingTime < 1000, "处理${tokens.size}个token应该在1秒内完成，实际用时：${processingTime}ms")
    }
}
