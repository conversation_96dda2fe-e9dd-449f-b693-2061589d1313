package com.example.gymbro.features.thinkingbox.domain.mapper

import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * DomainMapper单元测试
 *
 * 🎯 测试目标：验证Segment队列架构映射器的三断点规则
 * 📊 覆盖率目标：≥90%
 * 🔧 测试框架：JUnit 5 + kotlin.test
 * 🔥 重点：P0修复后的perthink闭合逻辑验证
 */
@DisplayName("DomainMapper")
class DomainMapperTest {

    private lateinit var domainMapper: DomainMapper

    @BeforeEach
    fun setUp() {
        domainMapper = DomainMapper()
    }

    @Nested
    @DisplayName("标签开启事件映射测试")
    inner class TagOpenedMappingTests {

        @Test
        @DisplayName("think标签应该创建PERTHINK段")
        fun `think tag should create PERTHINK segment`() {
            // Given
            val thinkEvent = SemanticEvent.TagOpened("think", emptyMap())
            val context = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(thinkEvent, context)

            // Then
            assertEquals(1, result.events.size)
            val segmentStarted = result.events[0] as ThinkingEvent.SegmentStarted
            assertEquals("perthink", segmentStarted.id)
            assertEquals(SegmentKind.PERTHINK, segmentStarted.kind)
            assertEquals("Bro is thinking", segmentStarted.title)

            // 验证上下文更新
            assertEquals("perthink", result.context.currentSegmentId)
            assertEquals(SegmentKind.PERTHINK, result.context.currentKind)
        }

        @Test
        @DisplayName("thinking标签应该强制闭合perthink段")
        fun `thinking tag should force close perthink segment`() {
            // Given
            val thinkingEvent = SemanticEvent.TagOpened("thinking", emptyMap())
            val contextWithPerthink = DomainMapper.MappingContext(
                currentSegmentId = "perthink",
                currentKind = SegmentKind.PERTHINK
            )

            // When
            val result = domainMapper.mapSemanticToThinking(thinkingEvent, contextWithPerthink)

            // Then
            assertEquals(1, result.events.size)
            val segmentClosed = result.events[0] as ThinkingEvent.SegmentClosed
            assertEquals("perthink", segmentClosed.id)

            // 验证上下文清理
            assertEquals(null, result.context.currentSegmentId)
            assertEquals(null, result.context.currentKind)
        }

        @Test
        @DisplayName("thinking标签在无当前段时应该发送保险措施闭合事件")
        fun `thinking tag should send insurance close event when no current segment`() {
            // Given
            val thinkingEvent = SemanticEvent.TagOpened("thinking", emptyMap())
            val emptyContext = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(thinkingEvent, emptyContext)

            // Then
            assertEquals(1, result.events.size)
            val segmentClosed = result.events[0] as ThinkingEvent.SegmentClosed
            assertEquals("perthink", segmentClosed.id)
        }

        @Test
        @DisplayName("phase标签应该创建新段并闭合当前段")
        fun `phase tag should create new segment and close current segment`() {
            // Given
            val phaseEvent = SemanticEvent.TagOpened("phase", mapOf("id" to "1"))
            val contextWithCurrent = DomainMapper.MappingContext(
                currentSegmentId = "perthink",
                currentKind = SegmentKind.PERTHINK,
                parseState = DomainMapper.ParsePhase.FORMAL_PHASE
            )

            // When
            val result = domainMapper.mapSemanticToThinking(phaseEvent, contextWithCurrent)

            // Then
            assertEquals(2, result.events.size)

            // 验证闭合事件
            val segmentClosed = result.events[0] as ThinkingEvent.SegmentClosed
            assertEquals("perthink", segmentClosed.id)

            // 验证新段创建
            val segmentStarted = result.events[1] as ThinkingEvent.SegmentStarted
            assertEquals("1", segmentStarted.id)
            assertEquals(SegmentKind.PHASE, segmentStarted.kind)

            // 验证上下文更新
            assertEquals("1", result.context.currentSegmentId)
            assertEquals(SegmentKind.PHASE, result.context.currentKind)
        }

        @Test
        @DisplayName("final标签应该激活final模式")
        fun `final tag should activate final mode`() {
            // Given
            val finalEvent = SemanticEvent.TagOpened("final", emptyMap())
            val context = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(finalEvent, context)

            // Then
            assertEquals(1, result.events.size)
            val finalStart = result.events[0] as ThinkingEvent.FinalStart
            assertTrue(result.context.inFinal)
        }
    }

    @Nested
    @DisplayName("标签关闭事件映射测试")
    inner class TagClosedMappingTests {

        @Test
        @DisplayName("think标签关闭应该不产生事件")
        fun `think tag close should not produce events`() {
            // Given
            val thinkCloseEvent = SemanticEvent.TagClosed("think")
            val context = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(thinkCloseEvent, context)

            // Then
            assertEquals(0, result.events.size)
        }

        @Test
        @DisplayName("thinking标签关闭应该触发ThinkingClosed")
        fun `thinking tag close should trigger ThinkingClosed`() {
            // Given
            val thinkingCloseEvent = SemanticEvent.TagClosed("thinking")
            val contextWithSegment = DomainMapper.MappingContext(
                currentSegmentId = "phase1",
                currentKind = SegmentKind.PHASE
            )

            // When
            val result = domainMapper.mapSemanticToThinking(thinkingCloseEvent, contextWithSegment)

            // Then
            assertEquals(4, result.events.size)

            // 验证事件序列
            assertTrue(result.events[0] is ThinkingEvent.SegmentClosed)
            assertTrue(result.events[1] is ThinkingEvent.SegmentStarted)
            assertTrue(result.events[2] is ThinkingEvent.SegmentClosed)
            assertTrue(result.events[3] is ThinkingEvent.ThinkingClosed)

            // 验证final-phase段创建
            val finalPhaseStart = result.events[1] as ThinkingEvent.SegmentStarted
            assertEquals("final-phase", finalPhaseStart.id)
            assertEquals(SegmentKind.FINAL_PHASE, finalPhaseStart.kind)
        }

        @Test
        @DisplayName("phase标签关闭应该闭合当前段")
        fun `phase tag close should close current segment`() {
            // Given
            val phaseCloseEvent = SemanticEvent.TagClosed("phase")
            val contextWithPhase = DomainMapper.MappingContext(
                currentSegmentId = "phase1",
                currentKind = SegmentKind.PHASE,
                parseState = DomainMapper.ParsePhase.FORMAL_PHASE
            )

            // When
            val result = domainMapper.mapSemanticToThinking(phaseCloseEvent, contextWithPhase)

            // Then
            assertEquals(1, result.events.size)
            val segmentClosed = result.events[0] as ThinkingEvent.SegmentClosed
            assertEquals("phase1", segmentClosed.id)

            // 验证上下文清理
            assertEquals(null, result.context.currentSegmentId)
            assertEquals(null, result.context.currentKind)
        }

        @Test
        @DisplayName("final标签关闭应该触发FinalComplete")
        fun `final tag close should trigger FinalComplete`() {
            // Given
            val finalCloseEvent = SemanticEvent.TagClosed("final")
            val contextInFinal = DomainMapper.MappingContext(inFinal = true)

            // When
            val result = domainMapper.mapSemanticToThinking(finalCloseEvent, contextInFinal)

            // Then
            assertEquals(1, result.events.size)
            assertTrue(result.events[0] is ThinkingEvent.FinalComplete)
            assertFalse(result.context.inFinal)
        }
    }

    @Nested
    @DisplayName("文本块事件映射测试")
    inner class TextChunkMappingTests {

        @Test
        @DisplayName("final模式下的文本应该映射为FinalContent")
        fun `text in final mode should map to FinalContent`() {
            // Given
            val textEvent = SemanticEvent.TextChunk("这是最终答案")
            val contextInFinal = DomainMapper.MappingContext(inFinal = true)

            // When
            val result = domainMapper.mapSemanticToThinking(textEvent, contextInFinal)

            // Then
            assertEquals(1, result.events.size)
            val finalContent = result.events[0] as ThinkingEvent.FinalContent
            assertEquals("这是最终答案", finalContent.text)
        }

        @Test
        @DisplayName("当前段内的文本应该映射为SegmentText")
        fun `text in current segment should map to SegmentText`() {
            // Given
            val textEvent = SemanticEvent.TextChunk("段内容")
            val contextWithSegment = DomainMapper.MappingContext(
                currentSegmentId = "phase1",
                currentKind = SegmentKind.PHASE
            )

            // When
            val result = domainMapper.mapSemanticToThinking(textEvent, contextWithSegment)

            // Then
            assertEquals(1, result.events.size)
            val segmentText = result.events[0] as ThinkingEvent.SegmentText
            assertEquals("段内容", segmentText.text)
        }

        @Test
        @DisplayName("无当前段时的非空文本应该创建perthink段")
        fun `non-empty text without current segment should create perthink segment`() {
            // Given
            val textEvent = SemanticEvent.TextChunk("我正在思考...")
            val emptyContext = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(textEvent, emptyContext)

            // Then
            assertEquals(2, result.events.size)

            // 验证段创建
            val segmentStarted = result.events[0] as ThinkingEvent.SegmentStarted
            assertEquals("perthink", segmentStarted.id)
            assertEquals(SegmentKind.PERTHINK, segmentStarted.kind)

            // 验证文本添加
            val segmentText = result.events[1] as ThinkingEvent.SegmentText
            assertEquals("我正在思考...", segmentText.text)

            // 验证上下文更新
            assertEquals("perthink", result.context.currentSegmentId)
            assertEquals(SegmentKind.PERTHINK, result.context.currentKind)
        }

        @Test
        @DisplayName("空白文本应该被忽略")
        fun `empty text should be ignored`() {
            // Given
            val emptyTextEvent = SemanticEvent.TextChunk("   ")
            val emptyContext = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(emptyTextEvent, emptyContext)

            // Then
            assertEquals(0, result.events.size)
        }
    }

    @Nested
    @DisplayName("Final事件直接映射测试")
    inner class FinalEventMappingTests {

        @Test
        @DisplayName("FinalStart应该直接映射")
        fun `FinalStart should be mapped directly`() {
            // Given
            val finalStartEvent = SemanticEvent.FinalStart
            val context = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(finalStartEvent, context)

            // Then
            assertEquals(1, result.events.size)
            assertTrue(result.events[0] is ThinkingEvent.FinalStart)
            assertTrue(result.context.inFinal)
        }

        @Test
        @DisplayName("FinalChunk应该直接映射")
        fun `FinalChunk should be mapped directly`() {
            // Given
            val finalChunkEvent = SemanticEvent.FinalChunk("最终内容")
            val context = DomainMapper.MappingContext()

            // When
            val result = domainMapper.mapSemanticToThinking(finalChunkEvent, context)

            // Then
            assertEquals(1, result.events.size)
            val finalContent = result.events[0] as ThinkingEvent.FinalContent
            assertEquals("最终内容", finalContent.text)
        }

        @Test
        @DisplayName("FinalEnd应该直接映射")
        fun `FinalEnd should be mapped directly`() {
            // Given
            val finalEndEvent = SemanticEvent.FinalEnd
            val contextInFinal = DomainMapper.MappingContext(inFinal = true)

            // When
            val result = domainMapper.mapSemanticToThinking(finalEndEvent, contextInFinal)

            // Then
            assertEquals(1, result.events.size)
            assertTrue(result.events[0] is ThinkingEvent.FinalComplete)
            assertFalse(result.context.inFinal)
        }
    }

    @Nested
    @DisplayName("弃用事件处理测试")
    inner class DeprecatedEventHandlingTests {

        @Test
        @DisplayName("弃用事件应该被忽略")
        fun `deprecated events should be ignored`() {
            // Given
            val deprecatedEvents = listOf(
                SemanticEvent.PreThinkChunk("content"),
                SemanticEvent.PreThinkEnd,
                SemanticEvent.PhaseStart("id", "title"),
                SemanticEvent.PhaseContent("id", "content"),
                SemanticEvent.PhaseEnd("id"),
                SemanticEvent.PhaseTitleUpdate("id", "title")
            )
            val context = DomainMapper.MappingContext()

            deprecatedEvents.forEach { event ->
                // When
                val result = domainMapper.mapSemanticToThinking(event, context)

                // Then
                assertEquals(0, result.events.size, "弃用事件 ${event::class.simpleName} 应该被忽略")
            }
        }
    }
}
