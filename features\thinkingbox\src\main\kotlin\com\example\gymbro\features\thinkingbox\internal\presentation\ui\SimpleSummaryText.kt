package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.ColorTokens

/**
 * SimpleSummaryText - 简单摘要文本组件
 *
 * 使用完整的设计系统Tokens，零硬编码
 *
 * @param content 摘要内容
 * @param modifier 修饰符
 */
@Composable
fun SimpleSummaryText(
    content: String,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(Tokens.Radius.Card),
        color = ColorTokens.Dark.SurfaceVariant,
        tonalElevation = Tokens.Elevation.Small
    ) {
        Text(
            text = content,
            style = androidx.compose.ui.text.TextStyle(
                fontSize = Tokens.Typography.BodyMedium,
                fontWeight = FontWeight.Normal,
                color = ColorTokens.Dark.OnSurfaceVariant
            ),
            modifier = Modifier.padding(Tokens.Spacing.Medium),
        )
    }
}

@GymBroPreview
@Composable
private fun SimpleSummaryTextPreview() {
    GymBroTheme {
        SimpleSummaryText(
            content = "这是一个简单的摘要文本示例，用于展示组件的基本外观。",
        )
    }
}