package com.example.gymbro.features.workout.template.edit.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.data.TemplateDataMapper
import timber.log.Timber
import javax.inject.Inject

/**
 * Template Edit Intent 处理器
 *
 * 🎯 职责：
 * - 处理模板编辑相关的 Intent
 * - 纯函数式状态转换
 * - 遵循 MVI Golden Standard
 *
 * 📋 遵循标准：
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 */
class TemplateEditIntentHandlers @Inject constructor() {

    // === 模板生命周期管理 ===

    fun handleLoadTemplate(
        intent: TemplateEditContract.Intent.LoadTemplate,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.withEffect(
            state.copy(
                isLoading = true,
                error = null,
            ),
            TemplateEditContract.Effect.LoadTemplateData(intent.templateId),
        )
    }

    // === 保存相关 Intent 处理 ===

    fun handleSaveAsDraft(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("📄 SaveAsDraft转为Effect")
        
        // 🔥 MVI 2.0修复：确保状态正确转换，防止重复保存
        if (state.isSaving) {
            Timber.w("⚠️ 已在保存中，忽略重复的SaveAsDraft Intent")
            return ReduceResult.noChange(state)
        }
        
        return ReduceResult.withEffect(
            newState = state.copy(
                isSaving = true,
                error = null // 清除之前的错误
            ),
            effect = TemplateEditContract.Effect.SaveAsDraft,
        )
    }

    fun handleCreateAndSaveImmediately(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("⚡ CreateAndSaveImmediately转为Effect")
        
        // 🔥 MVI 2.0修复：确保状态正确转换
        if (state.isSaving) {
            Timber.w("⚠️ 已在保存中，忽略重复的CreateAndSaveImmediately Intent")
            return ReduceResult.noChange(state)
        }
        
        return ReduceResult.withEffect(
            newState = state.copy(
                isSaving = true,
                error = null
            ),
            effect = TemplateEditContract.Effect.CreateAndSaveImmediately,
        )
    }

    fun handlePublishTemplate(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        println("🔥 [CRITICAL-PUBLISH] handlePublishTemplate 被调用，模板='${state.templateName}'")
        println(
            "🔥 [CRITICAL-PUBLISH] 状态详情: templateDescription='${state.templateDescription}', currentUserId='${state.currentUserId}'",
        )
        Timber.tag(
            "CRITICAL-SAVE",
        ).i("🔥 [CRITICAL-PUBLISH] handlePublishTemplate 被调用，模板='${state.templateName}'")
        Timber.tag("CRITICAL-SAVE").i("🔥 [CRITICAL-PUBLISH] 动作数=${state.exercises.size}")

        // 🔥 MVI 2.0修复：添加发布前验证
        if (state.isSaving || state.isCreatingVersion) {
            Timber.w("⚠️ 已在保存或创建版本中，忽略重复的PublishTemplate Intent")
            return ReduceResult.noChange(state)
        }

        // 🔥 MVI 2.0修复：添加基础验证
        if (state.templateName.isBlank()) {
            return ReduceResult.withEffect(
                newState = state.copy(
                    error = com.example.gymbro.core.ui.text.UiText.DynamicString("模板名称不能为空")
                ),
                effect = TemplateEditContract.Effect.ShowError(
                    com.example.gymbro.core.ui.text.UiText.DynamicString("请先设置模板名称")
                )
            )
        }

        // 🔥 记录当前状态中的动作数据
        state.exercises.forEachIndexed { exerciseIndex, exercise ->
            Timber.tag(
                "CRITICAL-SAVE",
            ).i(
                "🔥 [CRITICAL-PUBLISH] 动作${exerciseIndex + 1}: ${exercise.exerciseName}, customSets=${exercise.customSets.size}",
            )
            exercise.customSets.forEachIndexed { setIndex, set ->
                Timber.tag(
                    "CRITICAL-SAVE",
                ).i(
                    "🔥 [CRITICAL-PUBLISH] 组${setIndex + 1}: weight=${set.targetWeight}, reps=${set.targetReps}, rest=${set.restTimeSeconds}s",
                )
            }
        }

        Timber.d("🚀 PublishTemplate转为Effect")
        return ReduceResult.withEffect(
            newState = state.copy(
                isSaving = true, 
                isCreatingVersion = true,
                error = null
            ),
            effect = TemplateEditContract.Effect.PublishTemplate,
        )
    }

    // === 模板基本信息编辑 ===

    fun handleUpdateTemplateName(
        intent: TemplateEditContract.Intent.UpdateTemplateName,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val newState = state.copy(
            templateName = intent.name,
            hasUnsavedChanges = true,
            autoSaveState = TemplateContract.AutoSaveState.Inactive,
        )

        return ReduceResult.stateOnly(newState)
    }

    fun handleUpdateTemplateDescription(
        intent: TemplateEditContract.Intent.UpdateTemplateDescription,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                templateDescription = intent.description,
                hasUnsavedChanges = true,
                autoSaveState = TemplateContract.AutoSaveState.Inactive,
            ),
        )
    }

    // === 模板数据设置 ===

    fun handleSetTemplate(
        intent: TemplateEditContract.Intent.SetTemplate,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 使用统一数据映射器进行 Domain → DTO → UI 转换
        val templateDto = TemplateDataMapper.mapDomainToDto(intent.template)
        val updatedState = TemplateDataMapper.mapDtoToState(
            dto = templateDto,
            currentState = state,
        )

        // 🔥 修复：完全禁用AutoSave，避免干扰用户操作
        return ReduceResult.stateOnly(updatedState)
    }

    fun handleSetCurrentUserId(
        intent: TemplateEditContract.Intent.SetCurrentUserId,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            state.copy(currentUserId = intent.userId),
        )

    fun handleSetVersionHistory(
        intent: TemplateEditContract.Intent.SetVersionHistory,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(versionHistory = intent.versions),
        )
    }

    // === 创建空模板 ===

    fun handleCreateEmptyTemplate(
        intent: TemplateEditContract.Intent.CreateEmptyTemplate,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val templateId = intent.templateId
        val userId = if (state.currentUserId.isNotEmpty()) {
            state.currentUserId
        } else {
            // 🔥 修复：用户ID为空表示还未初始化，返回错误状态
            Timber.e("❌ 创建模板时用户ID为空，用户可能未认证")
            return ReduceResult.withEffect(
                state.copy(
                    error = UiText.DynamicString("用户未认证，无法创建模板"),
                    isLoading = false,
                ),
                TemplateEditContract.Effect.ShowError(UiText.DynamicString("用户认证异常")),
            )
        }

        // 创建空的模板对象
        val emptyTemplate = com.example.gymbro.domain.workout.model.template.WorkoutTemplate(
            id = templateId,
            name = "训练模版", // 🔥 设置默认名称
            description = "",
            targetMuscleGroups = emptyList(),
            difficulty = 1,
            estimatedDuration = 30,
            userId = userId,
            isPublic = false,
            isFavorite = false,
            tags = emptyList(),
            exercises = emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            currentVersion = 1,
            isDraft = true,
            isPublished = false,
            lastPublishedAt = null,
        )

        return ReduceResult.withEffect(
            newState = state.copy(
                template = emptyTemplate,
                templateName = "训练模版", // 🔥 设置状态中的默认名称
                templateDescription = "",
                exercises = emptyList(),
                isDraft = true,
                isPublished = false,
                currentVersion = 1,
                hasUnsavedChanges = false,
                isLoading = false,
                error = null,
            ),
            effect = TemplateEditContract.Effect.CreateAndSaveImmediately,
        )
    }
}
