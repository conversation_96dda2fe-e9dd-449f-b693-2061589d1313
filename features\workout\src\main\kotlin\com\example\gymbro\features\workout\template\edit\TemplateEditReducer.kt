package com.example.gymbro.features.workout.template.edit

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.reducer.ExerciseManagementHandlers
import com.example.gymbro.features.workout.template.edit.reducer.StateManagementHandlers
import com.example.gymbro.features.workout.template.edit.reducer.TemplateEditIntentHandlers
import com.example.gymbro.features.workout.template.edit.reducer.UIInteractionHandlers
import timber.log.Timber
import javax.inject.Inject

/**
 * TemplateEdit Reducer - MVI 2.0 架构核心（重构版）
 *
 * 🎯 职责：
 * - 纯函数式状态管理
 * - Intent 路由和分发
 * - 遵循 GymBro MVI Golden Standard
 *
 * 📋 遵循标准：
 * - MVI 2.0 架构模式
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 * - 模块化处理器设计
 *
 * 🔄 数据流：
 * Intent → Reducer → Handler → (State + Effect) → EffectHandler
 *
 * 🏗️ 架构设计：
 * - TemplateEditIntentHandlers: 模板基础操作
 * - ExerciseManagementHandlers: 动作管理操作
 * - UIInteractionHandlers: UI 交互操作
 * - StateManagementHandlers: 状态管理操作
 */
class TemplateEditReducer @Inject constructor(
    private val templateEditHandlers: TemplateEditIntentHandlers,
    private val exerciseHandlers: ExerciseManagementHandlers,
    private val uiHandlers: UIInteractionHandlers,
    private val stateHandlers: StateManagementHandlers,
) : Reducer<TemplateEditContract.Intent, TemplateEditContract.State, TemplateEditContract.Effect> {

    override fun reduce(
        intent: TemplateEditContract.Intent,
        currentState: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 🔥 只保留关键 Intent 的调试日志
        when (intent) {
            is TemplateEditContract.Intent.UpdateExercise,
            is TemplateEditContract.Intent.PublishTemplate,
            -> {
                WorkoutLogUtils.Template.debug("🎯 处理Intent: ${intent::class.simpleName}")
            }
            else -> {
                // 其他 Intent 不输出日志
            }
        }

        return when (intent) {
            // === 模板生命周期管理 ===
            is TemplateEditContract.Intent.LoadTemplate ->
                templateEditHandlers.handleLoadTemplate(intent, currentState)

            // === 保存相关操作 ===
            is TemplateEditContract.Intent.SaveAsDraft ->
                templateEditHandlers.handleSaveAsDraft(currentState)

            is TemplateEditContract.Intent.CreateAndSaveImmediately ->
                templateEditHandlers.handleCreateAndSaveImmediately(currentState)

            is TemplateEditContract.Intent.PublishTemplate ->
                templateEditHandlers.handlePublishTemplate(currentState)

            // === 模板发布结果处理 ===
            is TemplateEditContract.Intent.PublishCompleted -> {
                Timber.d("🔥 [PUBLISH-COMPLETED] 更新状态: isDraft=false, isPublished=true")
                // 🔥 P0修复：确保状态正确同步，并触发UI刷新Effect
                val updatedState = currentState.copy(
                    isDraft = false,
                    isPublished = true,
                    isSaving = false,
                    isCreatingVersion = false,
                    hasUnsavedChanges = false,
                    lastPublishedAt = System.currentTimeMillis(),
                    autoSaveState = com.example.gymbro.features.workout.template.TemplateContract.AutoSaveState.Success,
                )
                ReduceResult.withEffect(
                    newState = updatedState,
                    effect = TemplateEditContract.Effect.ShowTemplatePublished
                )
            }

            // === 模板基本信息编辑 ===
            is TemplateEditContract.Intent.UpdateTemplateName ->
                templateEditHandlers.handleUpdateTemplateName(intent, currentState)

            is TemplateEditContract.Intent.UpdateTemplateDescription ->
                templateEditHandlers.handleUpdateTemplateDescription(intent, currentState)

            // === 模板数据设置 ===
            is TemplateEditContract.Intent.SetTemplate ->
                templateEditHandlers.handleSetTemplate(intent, currentState)

            is TemplateEditContract.Intent.SetCurrentUserId ->
                templateEditHandlers.handleSetCurrentUserId(intent, currentState)

            is TemplateEditContract.Intent.SetVersionHistory ->
                templateEditHandlers.handleSetVersionHistory(intent, currentState)

            is TemplateEditContract.Intent.CreateEmptyTemplate ->
                templateEditHandlers.handleCreateEmptyTemplate(intent, currentState)

            // === 动作管理 ===
            is TemplateEditContract.Intent.AddExercise ->
                exerciseHandlers.handleAddExercise(intent, currentState)

            is TemplateEditContract.Intent.AddExercises ->
                exerciseHandlers.handleAddExercises(intent, currentState)

            is TemplateEditContract.Intent.UpdateExercise ->
                exerciseHandlers.handleUpdateExercise(intent, currentState)

            is TemplateEditContract.Intent.RemoveExercise ->
                exerciseHandlers.handleRemoveExercise(intent, currentState)

            is TemplateEditContract.Intent.QuickDuplicateExercise ->
                exerciseHandlers.handleQuickDuplicateExercise(intent, currentState)

            is TemplateEditContract.Intent.QuickDeleteExercise ->
                exerciseHandlers.handleQuickDeleteExercise(intent, currentState)

            // === UI 交互 ===
            is TemplateEditContract.Intent.NavigateBack ->
                uiHandlers.handleNavigateBack(currentState)

            is TemplateEditContract.Intent.ShowExerciseSelector ->
                uiHandlers.handleShowExerciseSelector(currentState)

            is TemplateEditContract.Intent.ResetNavigationState ->
                uiHandlers.handleResetNavigationState(currentState)

            is TemplateEditContract.Intent.ClearError ->
                uiHandlers.handleClearError(currentState)

            is TemplateEditContract.Intent.HandleError ->
                uiHandlers.handleError(intent, currentState)

            // === 对话框管理 ===
            is TemplateEditContract.Intent.ShowTemplateNameDialog ->
                uiHandlers.handleShowTemplateNameDialog(currentState)

            is TemplateEditContract.Intent.ShowTemplateDescriptionDialog ->
                uiHandlers.handleShowTemplateDescriptionDialog(currentState)

            is TemplateEditContract.Intent.DismissDialog ->
                uiHandlers.handleDismissDialog(currentState)

            is TemplateEditContract.Intent.UpdateTempTemplateName ->
                uiHandlers.handleUpdateTempTemplateName(intent, currentState)

            is TemplateEditContract.Intent.UpdateTempTemplateDescription ->
                uiHandlers.handleUpdateTempTemplateDescription(intent, currentState)

            is TemplateEditContract.Intent.ConfirmTemplateName ->
                uiHandlers.handleConfirmTemplateName(currentState)

            is TemplateEditContract.Intent.ConfirmTemplateDescription ->
                uiHandlers.handleConfirmTemplateDescription(currentState)

            // === 拖拽排序 ===
            is TemplateEditContract.Intent.StartDrag ->
                uiHandlers.handleStartDrag(intent, currentState)

            is TemplateEditContract.Intent.UpdateDragPosition ->
                uiHandlers.handleUpdateDragPosition(intent, currentState)

            is TemplateEditContract.Intent.CompleteDrag ->
                uiHandlers.handleCompleteDrag(intent, currentState)

            is TemplateEditContract.Intent.CancelDrag ->
                uiHandlers.handleCancelDrag(currentState)

            // === 快速操作 ===
            is TemplateEditContract.Intent.ShowQuickActions ->
                uiHandlers.handleShowQuickActions(intent, currentState)

            is TemplateEditContract.Intent.HideQuickActions ->
                uiHandlers.handleHideQuickActions(currentState)

            // === 状态管理 ===
            is TemplateEditContract.Intent.TriggerAutoSave ->
                stateHandlers.handleAutoSaveTriggered(currentState)

            is TemplateEditContract.Intent.AutoSaveStateChanged ->
                stateHandlers.handleAutoSaveStateChanged(intent, currentState)

            is TemplateEditContract.Intent.ShowVersionHistory ->
                stateHandlers.handleShowVersionHistory(currentState)

            is TemplateEditContract.Intent.HideVersionHistory ->
                stateHandlers.handleHideVersionHistory(currentState)

            is TemplateEditContract.Intent.RestoreFromVersion ->
                stateHandlers.handleRestoreFromVersion(intent, currentState)

            is TemplateEditContract.Intent.VersionCreated ->
                stateHandlers.handleVersionCreated(intent, currentState)

            is TemplateEditContract.Intent.SaveSuccess ->
                stateHandlers.handleSaveSuccess(currentState)

            is TemplateEditContract.Intent.DraftSaved -> {
                Timber.d("🔥 [DRAFT-SAVED] 更新状态: isDraft=true, hasUnsavedChanges=false")
                // 🔥 P0修复：确保草稿状态正确同步，并触发UI刷新Effect
                val updatedState = currentState.copy(
                    isSaving = false,
                    hasUnsavedChanges = false,
                    isDraft = true,
                    isPublished = false,
                    autoSaveState = com.example.gymbro.features.workout.template.TemplateContract.AutoSaveState.Success,
                    isCreatingVersion = false,
                )
                ReduceResult.withEffect(
                    newState = updatedState,
                    effect = TemplateEditContract.Effect.ShowDraftSaved
                )
            }

            // === 默认处理 ===
            else -> {
                Timber.w("🚨 [TemplateEditReducer] Unhandled intent: ${intent::class.simpleName}")
                ReduceResult.noChange(currentState)
            }
        }
    }
}
